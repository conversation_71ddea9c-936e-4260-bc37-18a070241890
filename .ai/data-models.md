# Data Models

## Data Entities

### User
- **Collection**: `users`
- **Key**: Firebase Auth UID
- **Fields**:
  - `id` (string): Firebase Auth UID
  - `email` (string): User's email address
  - `username` (string): Unique username
  - `created_at` (timestamp): Account creation time
  - `updated_at` (timestamp): Last update time
  - `is_active` (boolean): Account active status
- **Relationships**:
  - One-to-many with Workspaces (via workspace_members)
  - One-to-many with Profiles

### Workspace
- **Collection**: `workspaces`
- **Key**: Auto-generated UUID
- **Fields**:
  - `id` (string): Unique workspace ID
  - `name` (string): Workspace display name
  - `slug` (string): URL-friendly unique identifier
  - `description` (string, optional): Workspace description
  - `logo_url` (string, optional): Workspace logo
  - `ownerId` (string): User ID of workspace creator
  - `created_by` (string): Same as ownerId
  - `created_at` (timestamp): Creation time
  - `updated_at` (timestamp): Last update time
  - `deleted_at` (timestamp, optional): Soft delete timestamp
- **Relationships**:
  - Many-to-many with Users (via workspace_members)
  - One-to-many with Profiles

### WorkspaceMember
- **Collection**: `workspace_members`
- **Key**: Composite `{workspaceId}_{userId}`
- **Fields**:
  - `workspace_id` (string): Reference to workspace
  - `user_id` (string): Reference to user
  - `role` (string): One of 'owner', 'admin', 'member'
  - `created_at` (timestamp): Membership creation time
  - `updated_at` (timestamp): Last update time
- **Relationships**:
  - Belongs to User
  - Belongs to Workspace

### Profile
- **Collection**: `profiles`
- **Key**: Composite `{userId}_{workspaceId}`
- **Fields**:
  - `userId` (string): Reference to user
  - `workspace_id` (string): Reference to workspace
  - `display_name` (string, optional): Profile display name
  - `bio` (string, optional): Profile biography
  - `avatar_url` (string, optional): Profile picture URL
  - `created_at` (timestamp): Profile creation time
  - `updated_at` (timestamp): Last update time
  - `deleted_at` (timestamp, optional): Soft delete timestamp
- **Relationships**:
  - Belongs to User
  - Belongs to Workspace

## Data Sources

### Firebase Authentication
- **Type**: Identity Provider
- **Access Method**: Firebase Auth SDK
- **Data**: Authentication credentials, tokens, user metadata
- **Real-time**: Yes, auth state changes

### Cloud Firestore
- **Type**: NoSQL Document Database
- **Access Method**:
  - Direct Firebase SDK (auth collections)
  - Server API endpoints (business collections)
- **Data**: All application data models
- **Real-time**: Yes, via Firestore listeners

### HTTP Cookies
- **Type**: Browser Storage
- **Access Method**: Nuxt cookie utilities
- **Data**: Session information, user preferences
- **Real-time**: No, requires page reload

### Environment Variables
- **Type**: Configuration Storage
- **Access Method**: Runtime config
- **Data**: API keys, service URLs, feature flags
- **Real-time**: No, requires restart

## Data Pipeline

### Data Ingestion
1. **User Registration**:
   - Firebase Auth creates authentication record
   - Application creates User document
   - Default Workspace created
   - WorkspaceMember record links user as owner
   - Initial Profile created

2. **OAuth Login**:
   - OAuth provider returns user data
   - Firebase Auth creates/updates record
   - Application syncs user data
   - Missing data structures created if needed

### Data Processing
1. **Session Management**:
   - Auth state changes trigger session updates
   - Session data serialized to JSON
   - Cookie updated with new session
   - Server validates on each request

2. **Workspace Operations**:
   - Workspace changes update member records
   - Profile switches with workspace
   - Data filtering applies workspace context

### Data Storage
1. **Firestore Collections**:
   - Structured document storage
   - Automatic indexing
   - Query optimization
   - Real-time sync

2. **Cookie Storage**:
   - Limited to 4KB
   - HTTP-only for security
   - 7-day expiration
   - Server-side management

### Data Access
1. **Client Access**:
   - Direct Firestore for auth data
   - Composables abstract complexity
   - TypeScript ensures type safety
   - Real-time listeners for updates

2. **Server Access**:
   - Session validation from cookies
   - Firebase Admin SDK operations
   - Workspace-scoped queries
   - Error handling and logging

## Analytics & ML Models
*Currently not implemented, placeholder for future:*
- User behavior analytics
- Workspace usage patterns
- Security anomaly detection
- Recommendation systems

## Data Security & Privacy

### PII Data Handling
- **Identified PII**: email, username, display_name
- **Storage**: Encrypted at rest in Firestore
- **Transmission**: HTTPS only
- **Access**: Role-based with workspace scoping

### Data Retention Policies
- **Active Data**: Retained while account active
- **Soft Deletes**: Data marked with deleted_at timestamp
- **Hard Deletes**: Not implemented (soft delete only)
- **Backups**: Handled by Firebase infrastructure

### Access Controls
- **Authentication Required**: All data access requires valid session
- **Workspace Scoping**: Data filtered by workspace membership
- **Role-Based Access**: Owner > Admin > Member permissions
- **Field-Level Security**: Firebase rules enforce field access

### Compliance Considerations
- **GDPR**: User data exportable via Firestore
- **Data Portability**: JSON export format supported
- **Right to Deletion**: Soft delete implemented
- **Audit Trail**: Timestamps on all operations
