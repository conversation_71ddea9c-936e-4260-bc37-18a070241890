# Auth Module Integration Guide

## Quick Start Integration

### 1. Basic Authentication Check
```typescript
// In any component or composable
const { isAuthenticated, user } = useAuth()

if (!isAuthenticated.value) {
  // Redirect to login or show unauthenticated content
  await navigateTo('/auth/login')
  return
}

// Access user data
console.log('Current user:', user.value?.email)
```

### 2. Workspace-Aware Data Access
```typescript
// Using the data API with workspace context
const { currentWorkspace } = useAuth()
const campaignsApi = useDataApi('campaigns')

// Fetch workspace-scoped data
const campaigns = await campaignsApi.getAll({
  workspace_ids: [currentWorkspace.value?.id],
  limit: 20,
  orderBy: 'created_at',
  orderDirection: 'desc'
})
```

### 3. Role-Based Feature Control
```typescript
// Show/hide features based on user role
const { isWorkspaceOwner, isWorkspaceAdmin } = useAuth()

// In template
<BaseButton v-if="isWorkspaceOwner || isWorkspaceAdmin" @click="manageTeam">
  Manage Team
</BaseButton>

// In script
function canEditResource() {
  return isWorkspaceOwner.value || isWorkspaceAdmin.value
}
```

## Common Integration Patterns

### Protected Pages
```vue
<!-- pages/dashboard/settings.vue -->
<script setup>
definePageMeta({
  middleware: 'auth' // Requires authentication
})

const { currentWorkspace, isWorkspaceOwner } = useAuth()

// Additional permission check
onMounted(() => {
  if (!isWorkspaceOwner.value) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Only workspace owners can access settings'
    })
  }
})
</script>
```

### API Integration
```typescript
// When creating custom API endpoints
export default defineEventHandler(async (event) => {
  // Get session from auth module utilities
  const session = await getUserSession(event)

  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  // Use session data for operations
  const userId = session.user.id
  const workspaceId = session.currentWorkspace.id

  // Your API logic here
})
```

### Real-time Data Sync
```typescript
// Using Firebase directly for real-time features
const { firestore } = useFirebase()
const { currentWorkspace } = useAuth()

// Set up real-time listener
const unsubscribe = onSnapshot(
  query(
    collection(firestore, 'messages'),
    where('workspace_id', '==', currentWorkspace.value?.id),
    orderBy('created_at', 'desc'),
    limit(50)
  ),
  (snapshot) => {
    // Handle real-time updates
  }
)

// Clean up on unmount
onUnmounted(() => unsubscribe())
```

## Module Dependencies

### Required Composables
```typescript
// Always available via auto-import
import { useAuth } from '#imports'
import { useFirebase } from '#imports'
import { useDataApi } from '#imports'
```

### TypeScript Types
```typescript
// Import auth types for type safety
import type {
  Profile,
  User,
  Workspace,
  WorkspaceMember
} from '~/layers/auth-module/types'
```

## Error Handling Patterns

### Authentication Errors
```typescript
try {
  await login(email, password)
}
catch (error) {
  // Errors are automatically shown as toasts
  // But you can handle specific cases
  if (error.code === 'auth/user-not-found') {
    // Custom handling
  }
}
```

### Permission Errors
```typescript
const { currentWorkspace } = useAuth()

function checkPermission(resource: any) {
  if (resource.workspace_id !== currentWorkspace.value?.id) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Access denied to this resource'
    })
  }
}
```

## State Synchronization

### Workspace Switching
```typescript
// Listen for workspace changes
const { currentWorkspace } = useAuth()

watch(currentWorkspace, async (newWorkspace) => {
  if (newWorkspace) {
    // Reload workspace-specific data
    await refreshWorkspaceData()

    // Update UI state
    updateNavigationMenu()
  }
})
```

### Profile Updates
```typescript
// Profile automatically switches with workspace
const { currentProfile } = useAuth()

// Update profile data
const profilesApi = useDataApi('profiles')
await profilesApi.update(currentProfile.value.id, {
  display_name: 'New Name',
  bio: 'Updated bio'
})
```

## Testing with Auth

### Unit Tests
```typescript
// Mock auth state for testing
vi.mock('#imports', () => ({
  useAuth: () => ({
    user: ref({ id: 'test-user', email: '<EMAIL>' }),
    isAuthenticated: ref(true),
    currentWorkspace: ref({ id: 'test-workspace', name: 'Test' }),
    isWorkspaceOwner: computed(() => true)
  })
}))
```

### E2E Tests
```typescript
// Use test accounts with emulators
test('user can switch workspaces', async ({ page }) => {
  // Login with test account
  await page.goto('/auth/login')
  await page.fill('[name="email"]', '<EMAIL>')
  await page.fill('[name="password"]', 'testpass123')
  await page.click('button[type="submit"]')

  // Switch workspace
  await page.click('[data-test="workspace-selector"]')
  await page.click('text=Second Workspace')

  // Verify switch
  await expect(page.locator('[data-test="current-workspace"]'))
    .toContainText('Second Workspace')
})
```

## Performance Optimization

### Minimize Auth Checks
```typescript
// Bad: Multiple auth checks
if (isAuthenticated.value) { /* ... */ }
if (isAuthenticated.value) { /* ... */ }
if (isAuthenticated.value) { /* ... */ }

// Good: Single check with early return
if (!isAuthenticated.value)
  return
// All subsequent code is authenticated
```

### Cache Workspace Data
```typescript
// Use computed for derived state
const workspaceTeams = computed(() => {
  if (!currentWorkspace.value)
    return []
  return teams.value.filter(t =>
    t.workspace_id === currentWorkspace.value.id
  )
})
```

## Security Best Practices

1. **Never expose sensitive data in client code**
2. **Always validate workspace membership server-side**
3. **Use role helpers instead of manual role checks**
4. **Keep Firebase tokens server-side only**
5. **Follow the established error patterns**

## Common Pitfalls

1. **Accessing Firebase Auth directly** - Always use useAuth()
2. **Forgetting workspace context** - Data must be workspace-scoped
3. **Client-side only validation** - Always validate server-side too
4. **Storing sensitive data in localStorage** - Use server sessions
5. **Not handling loading states** - Show appropriate UI feedback
