# Auth Module Technology Stack

## Core Technologies

### Frontend Framework
- **Nuxt 3**: v3.16.2+ with Nitro server
- **Vue 3**: Composition API only
- **TypeScript**: v5.8.3 with strict mode

### UI Framework
- **Tairo UI**: Extended layer providing base components
- **Tailwind CSS**: v4.1.3 with custom configuration
- **Shuriken UI**: v4.0.0-beta.4 for additional components

### Authentication & Database
- **Firebase**: v11.9.1
  - Authentication (email/password + OAuth)
  - Firestore for data persistence
  - Admin SDK for server-side operations
- **Firebase Admin**: v13.4.0 for server-side auth

### Form Handling & Validation
- **VeeValidate**: v4.x for form management
- **Zod**: v3.x for schema validation
- **@vee-validate/zod**: Integration adapter

### Development Tools
- **Vite**: Build tool (via Nuxt)
- **ESLint**: Code linting with @antfu/eslint-config
- **Prettier**: Code formatting
- **VSCode**: Recommended IDE with Volar

## NPM Dependencies

### Production Dependencies
```json
{
  "firebase": "^11.9.1",
  "firebase-admin": "^13.4.0",
  "vee-validate": "^4.13.0",
  "@vee-validate/zod": "^4.13.0",
  "zod": "^3.23.0"
}
```

### Dev Dependencies
```json
{
  "@nuxt/test-utils": "^3.14.0",
  "@vue/test-utils": "^2.4.0",
  "@playwright/test": "^1.47.0",
  "@types/node": "^22.0.0"
}
```

## Configuration Files

### TypeScript Configuration
```typescript
// tsconfig.json
{
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitAny": true
  }
}
```

### Tailwind Configuration
```javascript
// tailwind.config.js
export default {
  content: [
    './components/**/*.{js,vue,ts}',
    './pages/**/*.vue',
    './layouts/**/*.vue'
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        auth: {
          primary: 'var(--auth-primary)',
          secondary: 'var(--auth-secondary)'
        }
      }
    }
  }
}
```

## Browser Support
- Chrome/Edge: Latest 2 versions
- Firefox: Latest 2 versions
- Safari: Latest 2 versions
- Mobile: iOS 14+, Android 10+

## Performance Targets
- Lighthouse Score: 90+
- First Contentful Paint: < 1.5s
- Time to Interactive: < 3s
- Bundle Size: < 200KB (auth module only)
