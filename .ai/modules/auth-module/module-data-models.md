# Auth Module Data Models

## Core Entities

### User
Primary authentication entity linked to Firebase Auth.

```typescript
interface User {
  id: string // Firebase Auth UID
  email: string // User email (unique)
  displayName: string // Display name
  photoURL?: string // Profile photo URL
  emailVerified: boolean // Email verification status
  phoneNumber?: string // Optional phone
  createdAt: Date // Account creation
  updatedAt: Date // Last update
  lastLoginAt: Date // Last login timestamp
  defaultWorkspaceId: string // Default workspace
  defaultProfileId: string // Default profile
  settings: UserSettings // User preferences
}

interface UserSettings {
  theme: 'light' | 'dark' | 'system'
  language: string
  notifications: NotificationSettings
  security: SecuritySettings
}
```

### Workspace
Organizational unit for multi-tenancy.

```typescript
interface Workspace {
  id: string // Firestore document ID
  name: string // Workspace name
  slug: string // URL-friendly identifier
  description?: string // Optional description
  logo?: string // Workspace logo URL
  ownerId: string // User ID of owner
  createdAt: Date // Creation timestamp
  updatedAt: Date // Last update
  settings: WorkspaceSettings
  subscription?: WorkspaceSubscription
  memberCount: number // Cached member count
  isActive: boolean // Active status
}

interface WorkspaceSettings {
  features: string[] // Enabled features
  limits: {
    maxMembers: number
    maxProfiles: number
    maxStorage: number // In MB
  }
  branding?: {
    primaryColor: string
    logo: string
  }
}

interface WorkspaceSubscription {
  plan: 'free' | 'pro' | 'enterprise'
  status: 'active' | 'cancelled' | 'expired'
  currentPeriodEnd: Date
}
```

### Profile
User personas that can be used across workspaces.

```typescript
interface Profile {
  id: string // Firestore document ID
  userId: string // Owner user ID
  name: string // Profile display name
  avatar?: string // Profile avatar URL
  bio?: string // Profile biography
  title?: string // Job title
  department?: string // Department/team
  skills: string[] // Skill tags
  metadata: Record<string, any> // Custom fields
  visibility: 'public' | 'workspace' | 'private'
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}
```

### WorkspaceProfile
Junction table for many-to-many relationship.

```typescript
interface WorkspaceProfile {
  id: string // Composite: workspaceId_profileId
  workspaceId: string // Reference to workspace
  profileId: string // Reference to profile
  userId: string // Denormalized for queries
  role: WorkspaceRole // Member role
  permissions: Permission[] // Specific permissions
  joinedAt: Date // When profile joined
  invitedBy?: string // Who invited (user ID)
  inviteAcceptedAt?: Date // Invite acceptance
  lastActiveAt: Date // Last activity
  status: 'active' | 'inactive' | 'suspended'
}

type WorkspaceRole = 'owner' | 'admin' | 'member' | 'guest'

type Permission
  = | 'workspace.manage'
    | 'members.invite'
    | 'members.remove'
    | 'billing.manage'
    | 'settings.update'
```

## Supporting Entities

### AuthSession
Tracks active user sessions.

```typescript
interface AuthSession {
  id: string // Session ID
  userId: string // User reference
  token: string // Session token (hashed)
  deviceInfo: {
    userAgent: string
    ip: string
    platform: string
  }
  createdAt: Date
  expiresAt: Date
  lastActiveAt: Date
  isActive: boolean
}
```

### WorkspaceInvite
Pending workspace invitations.

```typescript
interface WorkspaceInvite {
  id: string
  workspaceId: string
  invitedEmail: string
  invitedBy: string // User ID
  role: WorkspaceRole
  message?: string
  token: string // Secure invite token
  createdAt: Date
  expiresAt: Date
  status: 'pending' | 'accepted' | 'declined' | 'expired'
}
```

### AuditLog
Security and compliance logging.

```typescript
interface AuditLog {
  id: string
  userId: string
  workspaceId?: string
  action: AuditAction
  resource: string // Resource type
  resourceId: string // Resource ID
  changes?: Record<string, any>
  metadata: {
    ip: string
    userAgent: string
    location?: string
  }
  timestamp: Date
}

type AuditAction
  = | 'auth.login'
    | 'auth.logout'
    | 'auth.password_reset'
    | 'workspace.create'
    | 'workspace.update'
    | 'workspace.delete'
    | 'member.invite'
    | 'member.remove'
    | 'profile.create'
    | 'profile.update'
```

## Firestore Collections Structure

```
firestore/
├── users/{userId}
│   ├── settings (subcollection)
│   └── sessions (subcollection)
├── workspaces/{workspaceId}
│   ├── invites (subcollection)
│   └── audit_logs (subcollection)
├── profiles/{profileId}
├── workspace_profiles/{workspaceId}_{profileId}
└── system/
    ├── stats (document)
    └── config (document)
```

## Indexes Required

```javascript
// Firestore Indexes
workspaces:
-ownerId + createdAt (DESC)
- isActive + memberCount (DESC)

profiles:
-userId + createdAt (DESC)
- userId + isActive + name

workspace_profiles:
-workspaceId + status + joinedAt (DESC)
- profileId + status
- userId + workspaceId + status

audit_logs:
-workspaceId + timestamp (DESC)
- userId + timestamp (DESC)
- action + timestamp (DESC)
```

## Data Relationships

```
User (1) ─────┬──── (N) Workspace [as owner]
  │           │
  │ (1)       │ (N)
  │           │
  ├─ (N) Profile
  │           │
  │           │ (N:M via WorkspaceProfile)
  │           │
  └───────────┴──── Workspace
```

## Security Rules Summary

1. **Users**: Can only read/write their own data
2. **Workspaces**: Read based on membership, write based on role
3. **Profiles**: Public profiles readable by all, others by owner
4. **WorkspaceProfiles**: Managed by workspace admins/owners
5. **Audit Logs**: Read-only for workspace admins

## Data Migration Considerations

1. **User Migration**: Map existing auth systems to Firebase Auth
2. **Workspace Setup**: Default workspace creation for new users
3. **Profile Creation**: Auto-create default profile on registration
4. **Data Validation**: Ensure referential integrity
5. **Backwards Compatibility**: Support legacy auth tokens during transition
