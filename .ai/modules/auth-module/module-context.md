# Auth Module Context

## Module Overview
The Auth Module is a production-ready authentication and user management system for PIB applications, providing enterprise-grade multi-workspace support with Firebase integration and comprehensive session management.

## Key Features
- **Multi-workspace Architecture**: Users can belong to multiple workspaces with role-based access
- **Firebase Authentication**: Email/password and OAuth (Google implemented, Twitter/LinkedIn ready)
- **Session Management**: HTTP-only cookies with 7-day persistence
- **Role-Based Access Control**: Owner, Admin, Member roles per workspace
- **Profile System**: One profile per user per workspace (composite key: userId_workspaceId)
- **Hybrid Data Access**: Direct Firebase for auth, server API for business data
- **Real-time Updates**: Firestore listeners for workspace/profile changes
- **Graceful Error Recovery**: Auto-creates missing user data on login

## Technical Stack
- **Framework**: Nuxt 3 layer architecture with TypeScript strict mode
- **UI**: Tairo UI components + Vue 3 Composition API
- **Authentication**: Firebase Auth with server-side session management
- **Database**: Firebase Firestore with security rules
- **Validation**: Zod schemas + VeeValidate for forms
- **Styling**: Tailwind CSS v4 with dark mode support
- **State Management**: Vue composables (no Vuex/Pinia)

## Data Model Summary
- **User**: Core auth entity (Firebase UID as key)
  - Fields: id, email, username, created_at, updated_at, is_active
- **Workspace**: Organization context
  - Fields: id, name, slug, description, logo_url, ownerId
- **WorkspaceMember**: Links users to workspaces with roles
  - Key: {workspaceId}_{userId}
  - Roles: owner, admin, member
- **Profile**: User data within workspace context
  - Key: {userId}_{workspaceId}
  - Fields: display_name, bio, avatar_url

## Module Structure
```
layers/auth-module/
├── composables/    # Core logic (auth.ts, firebase.ts, useDataApi.ts)
├── pages/         # Auth pages (login, signup, recover)
├── server/        # API endpoints and utilities
│   ├── api/auth/  # Session management endpoints
│   └── utils/     # Session utilities
├── types/         # TypeScript definitions
└── nuxt.config.ts # Module configuration
```

## Key Composables

### `useAuth()` - Primary Authentication Interface
- **State**: user, currentWorkspace, currentProfile, workspaces, isAuthenticated
- **Methods**: login(), signup(), loginWithGoogle(), logout(), switchWorkspace()
- **Computed**: isWorkspaceOwner, isWorkspaceAdmin
- **Cookie Persistence**: Automatic state sync with server

### `useFirebase()` - Service Provider
- **Services**: auth, firestore, storage, messaging, vertexAI
- **Features**: Emulator support, session persistence, Google OAuth provider
- **Auto-cleanup**: On logout

### `useDataApi()` - Generic Data Operations
- **Operations**: create(), update(), remove(), getById(), getAll(), getByField()
- **Features**: Workspace-aware, automatic session inclusion, loading states
- **Pre-configured**: agencies, agents, teams, campaigns, etc.

## Integration Requirements

### For Dependent Modules
1. **Always use auth composables** - Never access Firebase directly
2. **Check authentication state**:
   ```typescript
   const { isAuthenticated } = useAuth()
   if (!isAuthenticated.value)
     return
   ```
3. **Access workspace context**:
   ```typescript
   const { currentWorkspace, currentProfile } = useAuth()
   ```
4. **Use permission helpers**:
   ```typescript
   const { isWorkspaceOwner, isWorkspaceAdmin } = useAuth()
   ```
5. **Make workspace-scoped API calls**:
   ```typescript
   const api = useDataApi('collection')
   await api.getAll({ workspace_ids: [currentWorkspace.value.id] })
   ```

## Security Patterns
- **HTTP-only Cookies**: Session storage prevents XSS attacks
- **CSRF Protection**: SameSite=lax cookie attribute
- **Field Validation**: Client and server-side with Zod
- **Consistent Naming**: camelCase throughout for Firebase rules
- **Token Isolation**: Firebase tokens stored server-side only
- **Error Handling**: Field-specific errors prevent information leakage

## Configuration Requirements
```typescript
// Required environment variables
NUXT_PUBLIC_FIREBASE_API_KEY
NUXT_PUBLIC_FIREBASE_AUTH_DOMAIN
NUXT_PUBLIC_FIREBASE_PROJECT_ID
NUXT_PUBLIC_FIREBASE_STORAGE_BUCKET
NUXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
NUXT_PUBLIC_FIREBASE_APP_ID
NUXT_PUBLIC_USE_FIREBASE_EMULATOR
```

## Development Guidelines
1. **Use Firebase Emulators**: For consistent local development
2. **Test Multi-workspace**: Always test workspace switching scenarios
3. **Handle Loading States**: Use isSubmitting, loading flags
4. **Follow Error Patterns**: Toast for general, field errors for specific
5. **Maintain Type Safety**: Import types from auth module
6. **Session Persistence**: Test across page reloads

## Current Limitations
- No 2FA/MFA support yet
- No session rotation mechanism
- Cookie size limited to 4KB
- No rate limiting implemented
- Manual token refresh required

## Future Enhancements (Out of Scope)
- Two-factor authentication
- SSO/SAML support
- Advanced audit logging
- Session storage service (Redis)
- Additional OAuth providers
