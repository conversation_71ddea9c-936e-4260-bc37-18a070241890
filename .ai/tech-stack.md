# Technology Stack

## Frontend Technologies
- **Framework**: Nuxt 3 (latest version)
- **UI Library**: Vue 3 with Composition API
- **State Management**: Vue composables (no Vuex/Pinia)
- **Styling**:
  - Tailwind CSS v4 with LightningCSS
  - Custom Tairo component library
  - Shuriken UI base components
- **Build Tools**:
  - Vite (via Nuxt)
  - TypeScript with strict mode
  - ESLint with @antfu/eslint-config

## Backend Technologies
- **Language**: TypeScript/JavaScript (Node.js >=22)
- **Framework**: Nuxt 3 server routes (Nitro engine)
- **API Style**: RESTful endpoints with JSON payloads
- **Authentication**:
  - Firebase Auth for identity management
  - HTTP-only cookies for session storage
  - Server-side session validation
- **Server/Hosting**:
  - Development: Local with Firebase emulators
  - Production: Compatible with any Node.js hosting

## Data Storage
- **Primary Database**: Cloud Firestore (NoSQL)
  - Collections: users, workspaces, profiles, workspace_members
  - Real-time listeners for live updates
  - Offline persistence support
- **Secondary Storage**:
  - Firebase Cloud Storage for files
  - Browser cookies for session data
- **Caching**:
  - Browser session persistence
  - Nuxt data caching for API responses
- **Data Access Pattern**:
  - Direct Firebase SDK for auth data
  - Server API endpoints for business data
  - Hybrid approach optimizes for real-time and security

## DevOps & Infrastructure
- **Version Control**: Git (GitHub/GitLab compatible)
- **CI/CD**:
  - GitHub Actions ready
  - Automated testing pipelines
  - Environment-based deployments
- **Deployment**:
  - Docker compatible
  - Vercel/Netlify ready
  - Traditional Node.js hosting supported
- **Monitoring**:
  - Console logging in development
  - Structured logging ready for production
  - Firebase Analytics integration possible
- **Cloud Provider**:
  - Firebase/Google Cloud Platform for services
  - Hosting provider agnostic for application

## Testing Frameworks
- **Unit Testing**: Vitest
  - Fast, Vite-powered test runner
  - Vue Test Utils for components
  - Mock Firebase services
- **Integration Testing**:
  - Vitest with Firebase emulators
  - API endpoint testing
  - Composable integration tests
- **E2E Testing**:
  - Playwright
  - Critical user flow coverage
  - Multi-browser support
- **Test Data Strategy**:
  - Firebase emulator data exports
  - Seed data scripts
  - Test user accounts

## Development Practices
- **Code Style**:
  - ESLint with Anthony Fu's config
  - Prettier compatible
  - Auto-fix on save
- **Documentation**:
  - JSDoc for complex functions
  - README files per module
  - CLAUDE.md for AI agent guidance
- **Code Quality Tools**:
  - TypeScript strict mode
  - ESLint for linting
  - Type checking in CI/CD
- **Package Manager**:
  - pnpm with workspaces
  - Efficient dependency management
  - Monorepo support

## Module-Specific Stack
### Authentication Module
- **Identity Provider**: Firebase Authentication
- **Session Management**: HTTP-only cookies
- **Password Validation**: Zod schemas
- **Form Handling**: VeeValidate
- **UI Components**: Tairo/BaseInput, BaseButton, etc.
- **OAuth Providers**: Google (configured), Twitter/LinkedIn (placeholders)

## Security Stack
- **Authentication**: Firebase Auth + custom session management
- **Authorization**: Role-based (Owner, Admin, Member)
- **Data Validation**: Zod schemas on client and server
- **CSRF Protection**: SameSite cookie attributes
- **Secrets Management**: Environment variables (.env files)
- **Security Rules**: Firebase Security Rules for Firestore

## Development Environment
- **Required Tools**:
  - Node.js >=22
  - pnpm package manager
  - Firebase CLI for emulators
  - Git for version control
- **IDE Support**:
  - VS Code recommended
  - Vue/TypeScript extensions
  - ESLint integration
- **Local Development**:
  - Firebase emulators for all services
  - Hot module replacement
  - TypeScript watch mode
