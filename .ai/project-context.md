# Project Context

## Project Overview
- **Project Name**: PIB (Tairo Nuxt Application with BMAD Agent System)
- **Description**: A modern Nuxt 3 application combining a sophisticated frontend with an AI-powered multi-agent orchestration framework for development workflows. Features a comprehensive multi-workspace authentication system built on Firebase.
- **Goals**:
  - Provide enterprise-grade B2B SaaS functionality with multi-workspace support
  - Enable AI-driven development through BMAD agent orchestration
  - Maintain excellent developer experience with modern tooling
  - Support scalable, secure authentication and authorization
- **Timeline**: Active development, modular expansion ongoing
- **Target Users**:
  - B2B SaaS customers requiring multi-workspace functionality
  - Development teams using AI agents for accelerated development
  - Enterprises requiring secure, scalable authentication

## Key Terminology
- **Workspace**: An organizational context that groups users, data, and permissions
- **Profile**: User-specific data within a workspace context (composite key: userId_workspaceId)
- **BMAD**: Breakthrough Method of Agile AI-driven Development
- **Layers**: Nuxt 3's modular architecture system for code organization
- **Composables**: Vue 3 composition API functions for reusable logic
- **Tairo**: Core UI layer providing components, layouts, and composables
- **HTTP-only Cookie**: Secure cookie accessible only by server, not client JavaScript
- **Hybrid Data Access**: <PERSON>tern using direct Firebase for auth, server API for business data

## Domain Knowledge
### Authentication Architecture
- Multi-workspace system where users can belong to multiple organizations
- Role-based access control with Owner, Admin, and Member roles
- Firebase Auth for identity management with server-side session handling
- Composite profile keys enable efficient workspace-profile relationships

### Module System
- Monorepo structure using pnpm workspaces
- Reusable feature modules in `/layers/` directory
- Apps compose functionality by extending multiple modules
- Each module is self-contained with its own nuxt.config.ts

### Development Workflow
- AI agents access project knowledge from `.ai/` directory
- Agents use standardized file naming (e.g., prd.md, architecture.md)
- Knowledge updates synchronize understanding across all agents
- Firebase emulators enable complete local development

## Project Constraints
### Technical Constraints
- Node.js version >=22 required
- Session data limited to ~4KB due to cookie storage
- Firebase security rules require camelCase field naming
- All business data must be workspace-scoped

### Business Constraints
- Must support unlimited workspaces per user
- Authentication must work offline with Firebase emulators
- Security must meet B2B enterprise requirements
- Module architecture must support independent deployment

### Timeline Constraints
- Modular development allows incremental feature delivery
- Core authentication is production-ready
- Additional modules can be added without disrupting existing functionality

### Resource Constraints
- Leverages Firebase's auto-scaling capabilities
- Stateless server design enables horizontal scaling
- Cookie-based sessions reduce server memory requirements

## Team Structure
- **Product Owner**: Bill (PM Agent) - Product strategy and requirements
- **Development Team**:
  - James (Full Stack Dev Agent)
  - Rodney (Frontend Dev Agent)
  - Various specialized agents for specific tasks
- **Stakeholders**:
  - AI Agent Orchestrator (BMAD)
  - Module developers requiring auth integration
  - End users of B2B SaaS applications

## Success Criteria
- All modules can integrate seamlessly with authentication
- Zero security vulnerabilities in auth implementation
- Sub-second authentication response times
- Support for 1000+ concurrent users per workspace
- 99.9% uptime for authentication services
- Complete local development capability with emulators
- Comprehensive documentation for AI agent understanding
