<script setup lang="ts">
export interface WidgetAccountBalanceProps {
  /**
   * The widget title
   */
  title?: string
  /**
   * The main balance amount
   */
  balance?: number
  /**
   * The daily change amount
   */
  dailyChange?: number
  /**
   * The date for the daily change
   */
  changeDate?: string
  /**
   * The trend direction
   */
  trend?: 'up' | 'down'
  /**
   * The trend icon name
   */
  trendIcon?: string
  /**
   * Whether to show the chart
   */
  showChart?: boolean
  /**
   * Currency symbol or format
   */
  currency?: string
}

const props = withDefaults(defineProps<WidgetAccountBalanceProps>(), {
  title: 'Account Balance',
  balance: 9543.12,
  dailyChange: 149.32,
  changeDate: 'Today, Sep 25',
  trend: 'up',
  trendIcon: 'lucide:arrow-up',
  showChart: true,
  currency: '$',
})

const trendIconClass = computed(() => {
  return props.trend === 'up' ? 'text-success-500' : 'text-danger-500'
})

const trendIcon = computed(() => {
  return props.trend === 'up' ? 'lucide:arrow-up' : 'lucide:arrow-down'
})
</script>

<template>
  <BaseCard
    rounded="md"
  >
    <div class="flex flex-col gap-4 px-8 pt-8 text-center">
      <BaseHeading
        as="h4"
        size="xs"
        weight="medium"
        lead="none"
        class="text-muted-700 dark:text-muted-100 uppercase"
      >
        {{ props.title }}
      </BaseHeading>
      <p>
        <span
          class="text-muted-900 font-sans text-4xl font-medium dark:text-white"
        >
          {{ props.currency }}{{ props.balance.toLocaleString() }}
        </span>
      </p>
      <div class="flex items-center justify-center gap-x-2">
        <Icon
          :name="trendIcon"
          class="iconify size-4" :class="[trendIconClass]"
        />
        <span class="text-muted-600 dark:text-muted-400 font-sans text-sm">
          {{ props.currency }}{{ props.dailyChange.toLocaleString() }} {{ props.changeDate }}
        </span>
      </div>
    </div>
    <ChartAreaBalance v-if="props.showChart" />
  </BaseCard>
</template>
