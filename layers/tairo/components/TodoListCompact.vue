<script setup lang="ts">
export interface TodoItem {
  id: string | number
  title: string
  description: string
  completed: boolean
}

export interface TodoListCompactProps {
  /**
   * The checkbox variant/color
   */
  variant?:
    | 'primary'
    | 'info'
    | 'success'
    | 'warning'
    | 'destructive'
    | 'muted'
    | 'light'
    | 'dark'
    | 'black'
    | 'custom'
  /**
   * Array of todo items
   */
  todos?: TodoItem[]
  /**
   * The v-model value for selected todos
   */
  modelValue?: string[]
  /**
   * Whether the list is disabled
   */
  disabled?: boolean
}

const props = withDefaults(defineProps<TodoListCompactProps>(), {
  variant: 'success',
  disabled: false,
  modelValue: () => [],
  todos: () => [
    {
      id: 0,
      title: 'Call Mr. <PERSON>',
      description: 'Review the project initial wireframes',
      completed: true,
    },
    {
      id: 1,
      title: 'Finish wireframes',
      description: 'Make all requested changes and publish',
      completed: false,
    },
    {
      id: 2,
      title: 'Update timesheets',
      description: 'Update all the team timesheets',
      completed: false,
    },
    {
      id: 3,
      title: 'Request payout',
      description: 'Send project invoice to client',
      completed: false,
    },
    {
      id: 4,
      title: 'Approve components',
      description: 'Review complete design system',
      completed: true,
    },
  ],
})

const emit = defineEmits<{
  'update:modelValue': [value: string[]]
}>()

const tasks = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})
</script>

<template>
  <BaseCheckboxGroup v-model="tasks" class="mb-2 space-y-6" :disabled="props.disabled">
    <label
      v-for="task in props.todos"
      :key="task.id"
      class="text-muted-300 flex cursor-pointer items-center gap-3"
      :class="{ 'opacity-50 cursor-not-allowed': props.disabled }"
    >
      <TairoCheckboxAnimated
        :variant="props.variant"
        :value="`Option ${task.id}`"
        :disabled="props.disabled"
      />
      <div>
        <BaseHeading
          as="h4"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ task.title }}</span>
        </BaseHeading>
        <BaseParagraph size="xs">
          <span class="text-muted-600 dark:text-muted-400">
            {{ task.description }}
          </span>
        </BaseParagraph>
      </div>
    </label>
  </BaseCheckboxGroup>
</template>
