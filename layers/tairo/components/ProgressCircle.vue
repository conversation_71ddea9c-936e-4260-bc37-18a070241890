<script setup lang="ts">
export interface ProgressCircleProps {
  /**
   * The title text to display
   */
  title?: string
  /**
   * The description text to display
   */
  text?: string
  /**
   * The image source for the avatar
   */
  image?: string
  /**
   * The progress value (0-100)
   */
  value?: number
  /**
   * The shape of the progress circle
   */
  shape?: 'straight' | 'rounded' | 'curved'
  /**
   * The size of the progress circle
   */
  size?: number
  /**
   * The maximum value for the progress
   */
  max?: number
  /**
   * The thickness of the progress circle
   */
  thickness?: number
  /**
   * The color variant of the progress circle
   */
  variant?: 'primary' | 'info' | 'success' | 'warning' | 'danger'
  /**
   * The size of the avatar
   */
  avatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  /**
   * Whether to show the title
   */
  showTitle?: boolean
  /**
   * Whether to show the description text
   */
  showText?: boolean
}

const props = withDefaults(defineProps<ProgressCircleProps>(), {
  value: 0,
  title: 'Progress Title',
  text: 'Progress description',
  image: '/img/avatars/10.svg',
  shape: 'rounded',
  size: 140,
  max: 100,
  thickness: 1,
  variant: 'primary',
  avatarSize: 'lg',
  showTitle: true,
  showText: true,
})
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="relative">
      <BaseProgressCircle
        :model-value="props.value"
        :size="props.size"
        :max="props.max"
        :thickness="props.thickness"
        :variant="props.variant"
      />
      <div
        class="absolute start-1/2 top-1/2 size-16 -translate-x-1/2 -translate-y-1/2"
      >
        <BaseAvatar :src="props.image" :size="props.avatarSize" />
      </div>
    </div>
    <div v-if="props.showTitle || props.showText" class="text-center">
      <BaseHeading
        v-if="props.showTitle"
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 mb-1 dark:text-white"
      >
        <span>{{ props.title }}</span>
      </BaseHeading>
      <BaseParagraph v-if="props.showText" size="xs">
        <span class="text-muted-600 dark:text-muted-400">{{ props.text }}</span>
      </BaseParagraph>
    </div>
  </div>
</template>
