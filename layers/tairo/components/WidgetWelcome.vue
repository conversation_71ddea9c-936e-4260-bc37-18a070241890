<script setup lang="ts">
export interface WidgetWelcomeProps {
  /**
   * The account title/label
   */
  accountTitle?: string
  /**
   * The user's name for the welcome message
   */
  userName?: string
  /**
   * The welcome message
   */
  welcomeMessage?: string
  /**
   * The description text
   */
  description?: string
  /**
   * The primary button text
   */
  buttonText?: string
  /**
   * The primary button action/link
   */
  buttonTo?: string
  /**
   * The button variant
   */
  buttonVariant?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'danger'
  /**
   * The button size
   */
  buttonSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /**
   * Whether to show the emoji in welcome message
   */
  showEmoji?: boolean
  /**
   * Custom emoji to use
   */
  emoji?: string
}

const props = withDefaults(defineProps<WidgetWelcomeProps>(), {
  accountTitle: 'Kendra\'s Account',
  userName: 'Kendra',
  welcomeMessage: 'Welcome back',
  description: 'Everything seems ok and up-to-date with your account since your last visit. Would you like to fund it?',
  buttonText: 'Fund my Account',
  buttonTo: '#',
  buttonVariant: 'primary',
  buttonSize: 'lg',
  showEmoji: true,
  emoji: '👋',
})
</script>

<template>
  <BaseCard
    rounded="md"
    class="h-full p-10"
  >
    <div class="flex h-full flex-col justify-between gap-5">
      <BaseHeading
        as="h4"
        size="xs"
        weight="medium"
        lead="none"
        class="text-muted-700 dark:text-muted-100 uppercase"
      >
        {{ props.accountTitle }}
      </BaseHeading>

      <h2
        class="font-heading text-muted-900 text-4xl font-medium dark:text-white"
      >
        {{ props.welcomeMessage }}, {{ props.userName }}!{{ props.showEmoji ? ` ${props.emoji}` : '' }}
      </h2>
      <BaseParagraph class="text-muted-600 dark:text-muted-400">
        {{ props.description }}
      </BaseParagraph>
      <BaseButton
        :variant="props.buttonVariant"
        rounded="md"
        :size="props.buttonSize"
        :to="props.buttonTo"
        class="w-full"
      >
        {{ props.buttonText }}
      </BaseButton>
    </div>
  </BaseCard>
</template>
