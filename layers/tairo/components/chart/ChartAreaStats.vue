<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

// Extended props for this specific chart with stats
interface ChartAreaStatsProps extends MultiSeriesChartProps {
  stats?: {
    new: number
    renewals: number
    resigns: number
  }
}

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<ChartAreaStatsProps>(), {
  // Chart dimensions
  height: 240,

  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'New Users',
      data: [44, 55, 57, 56, 61, 58, 63, 60, 66],
    },
    {
      name: 'Renewals',
      data: [76, 85, 101, 98, 87, 105, 91, 114, 94],
    },
    {
      name: 'Resigns',
      data: [35, 41, 36, 26, 45, 48, 52, 53, 41],
    },
  ],

  // Color scheme
  colors: () => ['var(--chart-color-base)', 'var(--color-indigo-500)', 'var(--color-primary-400)'],

  // Chart title
  title: '',

  // Animation and toolbar settings
  animations: false,
  toolbar: false,

  // Statistics to display
  stats: () => ({
    new: 314,
    renewals: 611,
    resigns: 49,
  }),
})

// Keep the original composable pattern but now use props
const areaSubscriptions = reactive(useAreaSubscriptions())

function useAreaSubscriptions() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        toolbar: {
          show: props.toolbar,
        },
        zoom: {
          enabled: false,
        },
        animations: {
          enabled: props.animations,
        },
        sparkline: {
          enabled: true,
        },
      },
      colors: props.colors,
      grid: {
        show: false,
        padding: {
          left: 0,
          right: 0,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2],
        curve: 'smooth',
      },
      xaxis: {
        type: 'numeric',
        axisBorder: {
          show: false,
        },
        labels: {
          show: false,
        },
      },
      yaxis: [
        {
          labels: {
            show: false,
          },
        },
      ],
      tooltip: {
        x: {
          show: false,
          format: 'dd/MM/yy HH:mm',
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.75,
        },
      },
    },
  })
}
</script>

<template>
  <div class="flex h-full flex-col">
    <div
      class="border-muted-300 dark:border-muted-800 mb-6 border-b p-6 text-center"
    >
      <div
        class="divide-muted-200 dark:divide-muted-800 flex w-full items-center divide-x"
      >
        <!-- Item -->
        <div class="flex-1">
          <div class="flex flex-col px-4 text-center">
            <h4
              class="text-muted-900 dark:text-muted-100 font-sans text-xl font-semibold"
            >
              {{ props.stats.new }}
            </h4>
            <p
              class="font-sans font-semibold text-muted-400 text-[0.65rem] uppercase"
            >
              New
            </p>
          </div>
        </div>
        <!-- Item -->
        <div class="flex-1">
          <div class="flex flex-col px-4 text-center">
            <h4
              class="text-muted-900 dark:text-muted-100 font-sans text-xl font-semibold"
            >
              {{ props.stats.renewals }}
            </h4>
            <p
              class="font-sans font-semibold text-muted-400 text-[0.65rem] uppercase"
            >
              Renewals
            </p>
          </div>
        </div>
        <!-- Item -->
        <div class="flex-1">
          <div class="flex flex-col px-4 text-center">
            <h4
              class="text-muted-900 dark:text-muted-100 font-sans text-xl font-semibold"
            >
              {{ props.stats.resigns }}
            </h4>
            <p
              class="font-sans font-semibold text-muted-400 text-[0.65rem] uppercase"
            >
              Resigns
            </p>
          </div>
        </div>
      </div>
    </div>
    <LazyAddonApexcharts v-bind="areaSubscriptions" class="mt-auto w-full" />
  </div>
</template>
