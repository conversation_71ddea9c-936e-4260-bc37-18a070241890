<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 380,
  series: () => [
    {
      name: 'Pending',
      data: [31, 40, 28, 51, 42, 109, 100],
    },
    {
      name: 'Completed',
      data: [11, 32, 45, 32, 34, 52, 41],
    },
    {
      name: 'Blocked',
      data: [78, 53, 36, 10, 14, 5, 2],
    },
  ],
  categories: () => [
    '2024-09-19T00:00:00.000Z',
    '2024-09-20T01:30:00.000Z',
    '2024-09-21T02:30:00.000Z',
    '2024-09-22T03:30:00.000Z',
    '2024-09-23T04:30:00.000Z',
    '2024-09-24T05:30:00.000Z',
    '2024-09-25T06:30:00.000Z',
  ],
  colors: () => ['var(--color-chart-base)', 'var(--color-indigo-600)', 'var(--color-amber-600)'],
  title: '',
  animations: false,
  toolbar: false,
})

const areaTaskCompletion = reactive(useAreaTaskCompletion())

function useAreaTaskCompletion() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
        zoom: {
          enabled: false,
        },
      },
      colors: props.colors,
      legend: {
        show: false,
        position: 'top',
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'smooth',
      },
      xaxis: {
        type: 'datetime',
        categories: props.categories,
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm',
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.1,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="areaTaskCompletion" />
</template>
