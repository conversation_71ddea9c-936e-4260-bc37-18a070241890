<script setup lang="ts">
import type { SimpleSingleSeriesChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<SimpleSingleSeriesChartProps>(), {
  // Chart dimensions
  height: 280,

  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'New members',
      data: [34, 44, 54, 21, 12, 43, 33, 23, 66, 66, 58, 79],
    },
  ],

  // X-axis categories - default matches original months
  categories: () => [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],

  // Color scheme
  colors: () => ['var(--color-chart-base)'],

  // Chart title
  title: '',

  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

// Keep the original composable pattern but now use props
const demoLineStep = reactive(useDemoLineStep())

function useDemoLineStep() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'line',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        toolbar: {
          show: props.toolbar,
        },
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'stepline',
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
      },
      title: {
        text: props.title,
        align: 'left',
      },
      markers: {
        hover: {
          sizeOffset: 4,
        },
      },
      xaxis: {
        categories: props.categories,
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoLineStep" />
</template>
