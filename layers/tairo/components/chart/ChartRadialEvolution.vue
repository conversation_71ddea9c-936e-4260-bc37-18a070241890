<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 220,
  series: () => [54],
  labels: () => ['Median Ratio'],
  colors: () => ['var(--color-chart-base)'],
  title: '',
  animations: false,
  toolbar: false,
  totalFormatter: () => '(30 days)',
})

const radialEvolution = reactive(useRadialEvolution())

function useRadialEvolution() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      colors: props.colors,
      title: {
        text: props.title,
        align: 'left',
      },
      chart: {
        toolbar: {
          show: props.toolbar,
        },
        animations: {
          enabled: props.animations,
        },
      },
      plotOptions: {
        radialBar: {
          dataLabels: {
            name: {
              offsetY: 15,
              fontSize: '13px',
              fontFamily: 'var(--font-alt)',
              color: 'var(--color-muted-400)',
            },
            value: {
              color: 'var(--color-muted-400)',
              offsetY: -20,
              fontSize: '16px',
              fontFamily: 'var(--font-sans)',
              fontWeight: '500',
            },
            total: {
              formatter: props.totalFormatter,
            },
          },
        },
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="radialEvolution" />
</template>
