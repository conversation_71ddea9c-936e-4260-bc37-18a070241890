<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 155,
  series: () => [65],
  labels: () => ['Progress'],
  colors: () => ['var(--color-chart-base)'],
  title: '',
  animations: false,
  toolbar: false,
})

const radialSalesRevenue = reactive(useRadialSalesRevenue())

function useRadialSalesRevenue() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      chart: {
        toolbar: {
          show: props.toolbar,
        },
        animations: {
          enabled: props.animations,
        },
      },
      colors: props.colors,
      plotOptions: {
        radialBar: {
          hollow: {
            size: '75%',
          },
          dataLabels: {
            show: true,
            name: {
              show: false,
              fontSize: '12px',
              fontFamily: 'var(--font-sans)',
              fontWeight: 400,
              offsetY: 5,
            },
            value: {
              show: true,
              fontWeight: 600,
              fontFamily: 'var(--font-sans)',
              fontSize: '16px',
              offsetY: 5,
            },
          },
        },
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="radialSalesRevenue" />
</template>
