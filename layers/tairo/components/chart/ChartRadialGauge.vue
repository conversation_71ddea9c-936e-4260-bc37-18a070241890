<script setup lang="ts">
import type { CustomChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<CustomChartProps>(), {
  height: 370,

  // Default series data - matches original implementation
  series: () => [67],

  // Color scheme
  colors: () => ['var(--color-chart-base)'],

  // Chart title
  title: '',

  // Animation and toolbar settings
  animations: false,
  toolbar: false,

  // Custom options for gauge-specific configuration
  customOptions: () => ({
    plotOptions: {
      radialBar: {
        startAngle: -135,
        endAngle: 135,
        dataLabels: {
          name: {
            fontSize: '14px',
            color: undefined,
          },
          value: {
            offsetY: 10,
            fontSize: '18px',
            color: undefined,
            formatter: (value: any) => `${value} %`,
          },
        },
      },
    },
    stroke: {
      dashArray: 4,
    },
    labels: ['Median Ratio'],
  }),
})

const demoRadialGauge = reactive(useDemoRadialGauge())

function useDemoRadialGauge() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      chart: {
        offsetY: -10,
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      ...props.customOptions,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoRadialGauge" />
</template>
