<script setup lang="ts">
/**
 * REFERENCE IMPLEMENTATION for Chart Component Refactoring
 *
 * This component demonstrates the pattern for adding props with defaults
 * to chart components while maintaining backward compatibility.
 *
 * KEY PATTERNS:
 * 1. Import type definitions from shared types file
 * 2. Define props with withDefaults for Vue 3 compatibility
 * 3. Extract ALL hardcoded data to props with same default values
 * 4. Keep the useChartName() composable pattern
 * 5. Use computed/reactive values to merge props with chart config
 */

import type { SimpleSingleSeriesChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<SimpleSingleSeriesChartProps>(), {
  // Chart dimensions
  height: 280,

  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'Inflation',
      data: [2.3, 3.1, 4.0, 10.1, 4.0, 3.6, 3.2, 2.3, 1.4, 0.8, 0.5, 0.2],
    },
  ],

  // X-axis categories - default matches original months
  categories: () => [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],

  // Color scheme
  colors: () => ['var(--color-chart-base)'],

  // Chart title
  title: '',

  // Animation and toolbar settings
  animations: false,
  toolbar: false,

  // Y-axis formatter
  yAxisFormatter: (value: any) => `${value} %`,
})

// Keep the original composable pattern but now use props
const Bar = reactive(useBar())

function useBar() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'bar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: 'top', // top, center, bottom
          },
        },
      },
      dataLabels: {
        enabled: false,
        formatter: props.yAxisFormatter,
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ['#304758'],
        },
      },
      xaxis: {
        categories: props.categories,
        position: 'top',
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            },
          },
        },
        tooltip: {
          enabled: true,
        },
      },
      yaxis: {
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false,
          formatter: props.yAxisFormatter,
        },
      },
      colors: props.colors,
      title: {
        text: props.title,
        align: 'left',
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="Bar" />
</template>
