<script setup lang="ts">
import type { SimpleSingleSeriesChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<SimpleSingleSeriesChartProps>(), {
  // Chart dimensions
  height: 290,

  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'Sales',
      data: [105, 414, 357, 511, 497, 621, 695, 912, 748],
    },
  ],

  // X-axis categories - default matches original months
  categories: () => [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
  ],

  // Color scheme
  colors: () => ['var(--color-chart-base)'],

  // Chart title
  title: '',

  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

// Keep the original composable pattern but now use props
const Line = reactive(useLine())

function useLine() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'line',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'straight',
      },
      title: {
        text: props.title,
        align: 'left',
      },
      grid: {
        row: {
          colors: ['transparent', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5,
        },
      },
      xaxis: {
        categories: props.categories,
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="Line" />
</template>
