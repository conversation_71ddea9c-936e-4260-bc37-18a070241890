<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 390,
  series: () => [
    {
      name: 'Stock Price',
      data: [42000, 45000, 43500, 47000, 46000, 48500, 49000, 51000, 52500, 53000, 51500, 54000],
    },
  ],
  categories: () => [
    '2023-01-01T00:00:00.000Z',
    '2023-02-01T00:00:00.000Z',
    '2023-03-01T00:00:00.000Z',
    '2023-04-01T00:00:00.000Z',
    '2023-05-01T00:00:00.000Z',
    '2023-06-01T00:00:00.000Z',
    '2023-07-01T00:00:00.000Z',
    '2023-08-01T00:00:00.000Z',
    '2023-09-01T00:00:00.000Z',
    '2023-10-01T00:00:00.000Z',
    '2023-11-01T00:00:00.000Z',
    '2023-12-01T00:00:00.000Z',
  ],
  colors: () => ['var(--color-chart-base)'],
  title: '',
  animations: false,
  toolbar: false,
  yAxisFormatter: (value: any) => formatPrice(value),
})

const areaStockPrice = reactive(useAreaStockPrice())

function useAreaStockPrice() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        zoom: {
          enabled: false,
        },
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'smooth',
      },
      colors: props.colors,
      legend: {
        show: false,
        position: 'top',
      },
      grid: {
        show: false,
        padding: {
          left: -10,
          right: 0,
          bottom: 10,
        },
      },
      xaxis: {
        type: 'datetime',
        categories: props.categories,
      },
      yaxis: {
        labels: {
          show: false,
          offsetX: -15,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm',
        },
        y: {
          formatter: props.yAxisFormatter,
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.75,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="areaStockPrice" />
</template>
