<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 380,
  series: () => [
    {
      name: 'Expenses',
      data: [318, 150, 49, 152.13, 421, 1009, 1220, 418, 113, 45],
    },
    {
      name: 'Earnings',
      data: [192, 263, 459, 312, 349, 527, 397, 351, 618, 511],
    },
  ],
  categories: () => [
    '2022-10-19T00:00:00.000Z',
    '2022-10-20T01:30:00.000Z',
    '2022-10-21T02:30:00.000Z',
    '2022-10-22T03:30:00.000Z',
    '2022-10-23T04:30:00.000Z',
    '2022-10-24T05:30:00.000Z',
    '2022-10-25T06:30:00.000Z',
    '2022-10-26T06:30:00.000Z',
    '2022-10-27T06:30:00.000Z',
    '2022-10-28T06:30:00.000Z',
  ],
  colors: () => ['var(--color-chart-base)', 'var(--color-indigo-600)', 'var(--color-amber-600)'],
  title: '',
  animations: false,
  toolbar: false,
  yAxisFormatter: (value: any) => formatPrice(value),
})

const incomeHistory = reactive(useAreaIncomeHistory())

function useAreaIncomeHistory() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        toolbar: {
          show: props.toolbar,
        },
        animations: {
          enabled: props.animations,
        },
        zoom: {
          enabled: false,
        },
      },
      colors: props.colors,
      title: {
        text: props.title,
        align: 'left',
      },
      legend: {
        show: false,
        position: 'top',
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'smooth',
      },
      xaxis: {
        type: 'datetime',
        categories: props.categories,
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm',
        },
        y: {
          formatter: props.yAxisFormatter,
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.1,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="incomeHistory" />
</template>
