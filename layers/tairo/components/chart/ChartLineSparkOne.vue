<script setup lang="ts">
import type { SparkChartProps } from '~/types/chart-props'

// Extended props for this specific spark chart
interface SparkLineChartProps extends SparkChartProps {
  seriesName?: string
  strokeCurve?: 'smooth' | 'straight' | 'stepline'
}

// Define props with proper TypeScript types and defaults
const props = withDefaults(defineProps<SparkLineChartProps>(), {
  // Chart dimensions
  width: undefined,
  height: 60,

  // Data - default matches original hardcoded data
  data: () => [2565, 6126, 4271, 5249, 2245, 4424, 1752, 3996, 976, 2157],

  // Color
  color: 'var(--color-chart-base)',

  // Chart type
  type: 'line',

  // Series name
  seriesName: 'Sales',

  // Stroke curve type
  strokeCurve: 'smooth',
})

// Keep the original composable pattern but now use props
const sparkLineOne = reactive(useLineSparkOne())

function useLineSparkOne() {
  const series = shallowRef([
    {
      name: props.seriesName,
      data: props.data,
    },
  ])

  return defineApexchartsProps({
    type: props.type as 'line',
    height: props.height,
    series,
    options: {
      chart: {
        id: 'sparkline1',
        sparkline: {
          enabled: true,
        },
        group: 'sparklines',
      },
      grid: {
        padding: {
          top: 10,
          right: 0,
          bottom: 0,
          left: 0,
        },
      },
      stroke: {
        curve: props.strokeCurve,
        width: [2],
      },
      markers: {
        size: 0,
      },
      yaxis: {
        min: 0,
        labels: {
          minWidth: 100,
        },
      },
      tooltip: {
        fixed: {
          enabled: true,
          position: 'right',
        },
        x: {
          show: false,
        },
      },
      colors: [props.color],
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="sparkLineOne" />
</template>
