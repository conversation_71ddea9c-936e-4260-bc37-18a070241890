<script setup lang="ts">
import type { SparkChartProps } from '~/types/chart-props'

// Extended props for this specific spark chart
interface SparkLineChartProps extends SparkChartProps {
  seriesName?: string
  strokeCurve?: 'smooth' | 'straight' | 'stepline'
  yFormatter?: (value: any) => string
}

// Define props with proper TypeScript types and defaults
const props = withDefaults(defineProps<SparkLineChartProps>(), {
  // Chart dimensions
  width: undefined,
  height: 60,

  // Data - default matches original hardcoded data
  data: () => [12.2, 14.5, 2.5, 47.5, 32.5, 44.5, 14.8, 55.5, 41.3, 69.7],

  // Color
  color: 'var(--color-success-500)',

  // Chart type
  type: 'line',

  // Series name
  seriesName: 'Income',

  // Stroke curve type
  strokeCurve: 'smooth',

  // Y-axis formatter
  yFormatter: (value: any) => `${formatPrice(value)}k`,
})

// Keep the original composable pattern but now use props
const sparkLineTwo = reactive(useLineSparkTwo())

function useLineSparkTwo() {
  const series = shallowRef([
    {
      name: props.seriesName,
      data: props.data,
    },
  ])

  return defineApexchartsProps({
    type: props.type as 'line',
    height: props.height,
    series,
    options: {
      chart: {
        id: 'sparkline2',
        sparkline: {
          enabled: true,
        },
        group: 'sparklines',
      },
      grid: {
        padding: {
          top: 10,
          right: 0,
          bottom: 0,
          left: 0,
        },
      },
      stroke: {
        curve: props.strokeCurve,
        width: [2],
      },
      markers: {
        size: 0,
      },
      yaxis: {
        min: 0,
        labels: {
          minWidth: 100,
        },
      },
      tooltip: {
        fixed: {
          enabled: true,
          position: 'right',
        },
        x: {
          show: false,
        },
        y: {
          formatter: props.yFormatter,
        },
      },
      colors: [props.color],
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="sparkLineTwo" />
</template>
