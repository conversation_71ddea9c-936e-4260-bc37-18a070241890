<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 295,
  series: () => [44, 55, 67, 83],
  labels: () => ['Apples', 'Oranges', 'Bananas', 'Berries'],
  colors: () => ['var(--color-chart-base)', 'var(--color-primary-300)', 'var(--color-amber-400)', 'var(--color-indigo-400)'],
  title: '',
  animations: false,
  toolbar: false,
  totalFormatter: () => '249',
})

const demoRadialMulti = reactive(useDemoRadialMulti())

function useDemoRadialMulti() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      chart: {
        toolbar: {
          show: props.toolbar,
        },
        animations: {
          enabled: props.animations,
        },
      },
      colors: props.colors,
      plotOptions: {
        radialBar: {
          dataLabels: {
            name: {
              fontSize: '22px',
            },
            value: {
              fontSize: '16px',
              offsetY: 5,
            },
            total: {
              show: true,
              label: 'Total',
              formatter: props.totalFormatter,
            },
          },
        },
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoRadialMulti" />
</template>
