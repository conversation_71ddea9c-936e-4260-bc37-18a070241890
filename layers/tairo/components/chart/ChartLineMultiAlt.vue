<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 280,
  series: generateDefaultSeries,
  title: '',
  animations: false,
  toolbar: false,
  colors: () => ['var(--color-chart-base)', 'var(--color-info-500)', 'var(--color-success-500)'],
  yAxisFormatter: (val: any) => (val / 1_000_000).toFixed(2),
})

// Helper function to generate default series data
function generateDefaultSeries() {
  const dataSeries = [
    [
      { date: '2014-01-01', value: 20000000 },
      { date: '2014-01-02', value: 10379978 },
      { date: '2014-01-03', value: 30493749 },
      { date: '2014-01-04', value: 10785250 },
      { date: '2014-01-05', value: 33901904 },
      { date: '2014-01-06', value: 11576838 },
      { date: '2014-01-07', value: 14413854 },
      { date: '2014-01-08', value: 15177211 },
      { date: '2014-01-09', value: 16622100 },
      { date: '2014-01-10', value: 17381072 },
      { date: '2014-01-11', value: 18802310 },
      { date: '2014-01-12', value: 15531790 },
    ],
    [
      { date: '2014-01-01', value: 150000000 },
      { date: '2014-01-02', value: 160379978 },
      { date: '2014-01-03', value: 170493749 },
      { date: '2014-01-04', value: 160785250 },
      { date: '2014-01-05', value: 167391904 },
      { date: '2014-01-06', value: 161576838 },
      { date: '2014-01-07', value: 161413854 },
      { date: '2014-01-08', value: 152177211 },
      { date: '2014-01-09', value: 140762210 },
      { date: '2014-01-10', value: 144381072 },
      { date: '2014-01-11', value: 154352310 },
      { date: '2014-01-12', value: 165531790 },
      { date: '2014-01-13', value: 175748881 },
      { date: '2014-01-14', value: 187064037 },
      { date: '2014-01-15', value: 197520685 },
      { date: '2014-01-16', value: 210176418 },
      { date: '2014-01-17', value: 196122924 },
      { date: '2014-01-18', value: 207337480 },
    ],
    [
      { date: '2014-01-01', value: 50000000 },
      { date: '2014-01-02', value: 60379978 },
      { date: '2014-01-03', value: 40493749 },
      { date: '2014-01-04', value: 60785250 },
      { date: '2014-01-05', value: 67391904 },
      { date: '2014-01-06', value: 61576838 },
      { date: '2014-01-07', value: 61413854 },
      { date: '2014-01-08', value: 82177211 },
      { date: '2014-01-09', value: 103762210 },
      { date: '2014-01-10', value: 84381072 },
      { date: '2014-01-11', value: 54352310 },
      { date: '2014-01-12', value: 65531790 },
    ],
  ]

  let ts1 = 1388534400000
  let ts2 = 1388620800000
  let ts3 = 1389052800000

  const dataSet: any[] = [[], [], []]

  for (let i = 0; i < 12; i++) {
    ts1 = ts1 + 86400000
    const innerArr: any[] = [ts1, dataSeries[2]![i]!.value]
    dataSet[0].push(innerArr)
  }
  for (let i = 0; i < 18; i++) {
    ts2 = ts2 + 86400000
    const innerArr: any[] = [ts2, dataSeries[1]![i]!.value]
    dataSet[1].push(innerArr)
  }
  for (let i = 0; i < 12; i++) {
    ts3 = ts3 + 86400000
    const innerArr: any[] = [ts3, dataSeries[0]![i]!.value]
    dataSet[2].push(innerArr)
  }

  return [
    { name: 'Desktops', data: dataSet[0] },
    { name: 'Phones', data: dataSet[1] },
    { name: 'Tablets', data: dataSet[2] },
  ]
}

const demoAreasMultiple = reactive(useDemoAreasMultiple())

function useDemoAreasMultiple() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        stacked: false,
        zoom: {
          enabled: false,
        },
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      dataLabels: {
        enabled: false,
      },
      markers: {
        size: 0,
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          inverseColors: false,
          opacityFrom: 0.45,
          opacityTo: 0.05,
          stops: [20, 100, 100, 100],
        },
      },
      yaxis: {
        labels: {
          style: {
            colors: '#8e8da4',
          },
          offsetX: 0,
          formatter: props.yAxisFormatter,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      xaxis: {
        type: 'datetime',
        tickAmount: 8,
        min: new Date('01/01/2014').getTime(),
        max: new Date('01/20/2014').getTime(),
        labels: {
          rotate: -15,
          rotateAlways: true,
          formatter: (val: any, timestamp: any) => timestamp?.toString() ?? '',
        },
      },
      colors: props.colors,
      title: {
        text: props.title,
        align: 'left',
      },
      tooltip: {
        shared: true,
      },
      stroke: {
        width: [2, 2, 2],
      },
      legend: {
        position: 'top',
        horizontalAlign: 'center',
      },
    },
  })
}
</script>

<template>
  <div class="relative">
    <BaseCard class="p-6">
      <!-- Title -->
      <div class="mb-6">
        <BaseHeading
          as="h3"
          size="md"
          weight="semibold"
          lead="tight"
          class="text-muted-800 dark:text-white"
        >
          <span>Multiple Areas</span>
        </BaseHeading>
      </div>
      <LazyAddonApexcharts v-bind="demoAreasMultiple" />
    </BaseCard>
  </div>
</template>
