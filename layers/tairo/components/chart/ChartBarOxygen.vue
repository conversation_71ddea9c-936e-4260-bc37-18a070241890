<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 280,
  series: () => [
    {
      name: 'Variation (pt)',
      data: [23, 26, 10, 7, 11, 18, 16],
    },
  ],
  categories: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  colors: () => ['var(--color-chart-base)'],
  title: '',
  animations: false,
  toolbar: false,
  yAxisFormatter: (value: any) => `${value} %`,
})

const barOxygen = reactive(useBarOxygen())

function useBarOxygen() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'bar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: 'top', // top, center, bottom
          },
        },
      },
      dataLabels: {
        enabled: true,
        formatter: props.yAxisFormatter,
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ['#304758'],
        },
      },
      grid: {
        show: false,
      },
      xaxis: {
        categories: props.categories,
        position: 'top',
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            },
          },
        },
        tooltip: {
          enabled: true,
        },
      },
      yaxis: {
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false,
          formatter: props.yAxisFormatter,
        },
      },
      colors: props.colors,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="barOxygen" />
</template>
