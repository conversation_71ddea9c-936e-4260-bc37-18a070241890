<script setup lang="ts">
import type { ScatterBubbleChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<ScatterBubbleChartProps>(), {
  height: 280,

  // Default series data - matches original implementation
  series: () => [
    {
      name: 'Bubble1',
      data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
        min: 10,
        max: 60,
      }),
    },
    {
      name: 'Bubble2',
      data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
        min: 10,
        max: 60,
      }),
    },
    {
      name: 'Bubble3',
      data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
        min: 10,
        max: 60,
      }),
    },
    {
      name: 'Bubble4',
      data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
        min: 10,
        max: 60,
      }),
    },
  ],

  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-amber-400)', 'var(--color-indigo-400)', 'var(--color-primary-300)'],

  // Chart title
  title: '',

  // Axis configuration
  yAxis: () => ({
    max: 70,
  }),

  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

// Helper function for generating bubble data
function generateData(
  baseval: number,
  count: number,
  yrange: { min: number, max: number },
) {
  let i = 0
  const _series = []
  while (i < count) {
    const x = Math.floor(Math.random() * (750 - 1 + 1)) + 1
    const y
      = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min
    const z = Math.floor(Math.random() * (75 - 15 + 1)) + 15

    _series.push([x, y, z])
    baseval += 86400000
    i++
  }
  return _series
}

const Bubble = reactive(useBubble())

function useBubble() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'bubble',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
      },
      fill: {
        opacity: 0.8,
      },
      title: {
        text: props.title,
      },
      xaxis: {
        tickAmount: 12,
        type: 'category',
        ...(props.xAxis || {}),
      },
      yaxis: {
        max: props.yAxis?.max || 70,
        ...(props.yAxis || {}),
      },
      legend: {
        position: 'top',
        horizontalAlign: 'center',
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="Bubble" />
</template>
