<script setup lang="ts">
import type { ScatterBubbleChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<ScatterBubbleChartProps>(), {
  height: 280,

  // Default series data - matches original implementation
  series: () => [
    {
      name: 'Team 1',
      data: generateDayWiseTimeSeries(
        new Date('11 Feb 2017 GMT').getTime(),
        20,
        {
          min: 10,
          max: 60,
        },
      ),
    },
    {
      name: 'Team 2',
      data: generateDayWiseTimeSeries(
        new Date('11 Feb 2017 GMT').getTime(),
        20,
        {
          min: 10,
          max: 60,
        },
      ),
    },
    {
      name: 'Team 3',
      data: generateDayWiseTimeSeries(
        new Date('11 Feb 2017 GMT').getTime(),
        30,
        {
          min: 10,
          max: 60,
        },
      ),
    },
    {
      name: 'Team 4',
      data: generateDayWiseTimeSeries(
        new Date('11 Feb 2017 GMT').getTime(),
        10,
        {
          min: 10,
          max: 60,
        },
      ),
    },
  ],

  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-primary-300)', 'var(--color-amber-400)', 'var(--color-indigo-400)'],

  // Chart title
  title: '',

  // Axis configuration
  yAxis: () => ({
    max: 70,
  }),

  // Animation and toolbar settings
  animations: true,
  toolbar: false,
})

// Helper function for generating scatter data
function generateDayWiseTimeSeries(
  baseval: number,
  count: number,
  yrange: { min: number, max: number },
) {
  let i = 0
  const series = []
  while (i < count) {
    const y
      = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min

    series.push([baseval, y])
    baseval += 86400000
    i++
  }
  return series
}

const demoScatter = reactive(useDemoScatter())

function useDemoScatter() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'scatter',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      chart: {
        zoom: {
          type: 'xy',
        },
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
      },
      grid: {
        xaxis: {
          lines: {
            show: true,
          },
        },
        yaxis: {
          lines: {
            show: true,
          },
        },
      },
      xaxis: {
        type: 'datetime',
        ...(props.xAxis || {}),
      },
      yaxis: {
        max: props.yAxis?.max || 70,
        ...(props.yAxis || {}),
      },
      legend: {
        position: 'top',
        horizontalAlign: 'center',
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoScatter" />
</template>
