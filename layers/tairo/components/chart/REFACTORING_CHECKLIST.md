# Chart Component Refactoring Checklist

## Reference Implementation
See `ChartBar.vue` for the complete pattern implementation.

## Required Steps for Each Chart Component

### 1. Import Type Definitions
```typescript
import type { [AppropriateInterface] } from '~/types/chart-props'
```
Choose the correct interface based on chart type:
- `SimpleSingleSeriesChartProps` - for single series charts
- `MultiSeriesChartProps` - for multi-series charts
- `DonutPieChartProps` - for donut/pie charts
- `RadialChartProps` - for radial/gauge charts
- `ScatterBubbleChartProps` - for scatter/bubble charts
- `RadarChartProps` - for radar charts
- `TimelineChartProps` - for timeline charts
- `SparkChartProps` - for spark charts
- `CustomChartProps` - for complex custom charts

### 2. Define Props with Defaults
```typescript
const props = withDefaults(defineProps<[AppropriateInterface]>(), {
  // ALL default values MUST match current hardcoded data exactly
})
```

### 3. Extract Hardcoded Data
Move ALL hardcoded values to props:
- [ ] Series data arrays
- [ ] Labels/categories
- [ ] Colors
- [ ] Title text
- [ ] Height/width values
- [ ] Formatter functions
- [ ] Any other chart-specific data

### 4. Update Composable Function
- [ ] Keep the `useChartName()` pattern
- [ ] Use `computed()` for reactive prop values if needed
- [ ] Replace hardcoded values with prop references

### 5. Validation Tests
Before marking complete, verify:
- [ ] Component renders without any props (uses defaults)
- [ ] Component renders exactly the same as before refactoring
- [ ] Props can override default values when provided
- [ ] TypeScript compiles without errors
- [ ] No `any` types used (except where absolutely necessary)

## Common Patterns

### Single Series Chart
```typescript
series: () => [
  {
    name: 'Series Name',
    data: [/* original data */]
  }
]
```

### Multi Series Chart
```typescript
series: () => [
  { name: 'Series 1', data: [/* data */] },
  { name: 'Series 2', data: [/* data */] }
]
```

### Categories/Labels
```typescript
categories: () => ['Label1', 'Label2', ...]
// OR
labels: () => ['Label1', 'Label2', ...]
```

### Colors
```typescript
colors: () => ['var(--color-chart-base)', 'var(--color-primary-300)', ...]
```

## Important Notes

1. **Backward Compatibility is CRITICAL** - Charts must work exactly as before when no props are provided
2. **Match Data Structure** - Keep the exact same data structure as the original
3. **Preserve Visual Output** - The chart should look identical with default props
4. **Use Proper Types** - Reference the interfaces in chart-props.ts
5. **Keep Original Logic** - Don't change chart behavior, only add prop support

## Example Usage After Refactoring

```vue
<!-- Using with default props (backward compatible) -->
<ChartBar />

<!-- Using with custom data -->
<ChartBar
  :series="[{ name: 'Sales', data: [10, 20, 30, 40] }]"
  :categories="['Q1', 'Q2', 'Q3', 'Q4']"
  title="Quarterly Sales"
/>
```
