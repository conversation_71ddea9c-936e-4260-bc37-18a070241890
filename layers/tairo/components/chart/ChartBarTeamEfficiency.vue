<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 380,
  series: () => [
    {
      name: 'Design',
      data: [-26, -15, -13, -14, -9, -12, -7, -10, -4],
    },
    {
      name: 'Development',
      data: [6, 15, 31, 28, 17, 35, 21, 44, 24],
    },
    {
      name: 'Management',
      data: [-35, -29, -34, -44, -25, -22, -18, -17, -29],
    },
  ],
  categories: () => [
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
  ],
  colors: () => ['var(--color-chart-base)', 'var(--color-indigo-400)', 'var(--color-indigo-500)'],
  title: '',
  animations: false,
  toolbar: false,
  yAxisFormatter: (value: any) => `${value} hours`,
})

const barTeamEfficiency = reactive(useBarTeamEfficiency())

function useBarTeamEfficiency() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'bar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      legend: {
        show: false,
        position: 'top',
      },
      plotOptions: {
        bar: {
          horizontal: false,
          // endingShape: 'rounded',
          columnWidth: '55%',
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent'],
      },
      xaxis: {
        categories: props.categories,
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: props.yAxisFormatter,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="barTeamEfficiency" />
</template>
