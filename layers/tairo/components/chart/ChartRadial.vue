<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 265,

  // Default series data - matches original implementation
  series: () => [70],

  // Labels
  labels: () => ['Power'],

  // Color scheme
  colors: () => ['var(--color-chart-base)'],

  // Chart title
  title: '',

  // Show value label
  showValue: true,

  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

const demoRadial = reactive(useDemoRadial())

function useDemoRadial() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      plotOptions: {
        radialBar: {
          hollow: {
            size: '70%',
          },
          dataLabels: {
            value: {
              show: props.showValue,
              fontSize: '16px',
              offsetY: 5,
            },
          },
        },
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoRadial" />
</template>
