<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 280,
  series: () => [
    {
      name: 'Spaceships',
      data: [400, 430, 448, 470, 540, 580, 690, 1100, 1200, 1380],
    },
  ],
  categories: () => [
    'South Korea',
    'Canada',
    'United Kingdom',
    'Netherlands',
    'Italy',
    'France',
    'Japan',
    'United States',
    'China',
    'Germany',
  ],
  colors: () => ['var(--color-chart-base)'],
  title: '',
  animations: false,
  toolbar: false,
})

const BarHorizontal = reactive(useBarHorizontal())

function useBarHorizontal() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'bar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      plotOptions: {
        bar: {
          horizontal: true,
        },
      },
      title: {
        text: props.title,
        align: 'left',
      },
      dataLabels: {
        enabled: false,
      },
      xaxis: {
        categories: props.categories,
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="BarHorizontal" />
</template>
