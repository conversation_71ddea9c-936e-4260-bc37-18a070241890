<script setup lang="ts">
import type { SparkChartProps } from '~/types/chart-props'

// Extended props for this specific spark chart
interface SparkLineChartProps extends SparkChartProps {
  seriesName?: string
  strokeCurve?: 'smooth' | 'straight' | 'stepline'
}

// Define props with proper TypeScript types and defaults
const props = withDefaults(defineProps<SparkLineChartProps>(), {
  // Chart dimensions
  width: undefined,
  height: 60,

  // Data - default matches original hardcoded data
  data: () => [4457, 4533, 7274, 3272, 5876, 3271, 4614, 3553, 4835, 1579],

  // Color
  color: 'var(--color-info-500)',

  // Chart type
  type: 'line',

  // Series name
  seriesName: 'New Orders',

  // Stroke curve type
  strokeCurve: 'smooth',
})

// Keep the original composable pattern but now use props
const sparkLineThree = reactive(useLineSparkThree())

function useLineSparkThree() {
  const series = shallowRef([
    {
      name: props.seriesName,
      data: props.data,
    },
  ])

  return defineApexchartsProps({
    type: props.type as 'line',
    height: props.height,
    series,
    options: {
      chart: {
        id: 'sparkline3',
        sparkline: {
          enabled: true,
        },
        group: 'sparklines',
      },
      grid: {
        padding: {
          top: 10,
          right: 0,
          bottom: 0,
          left: 0,
        },
      },
      stroke: {
        curve: props.strokeCurve,
        width: [2],
      },
      markers: {
        size: 0,
      },
      tooltip: {
        fixed: {
          enabled: true,
          position: 'right',
        },
        x: {
          show: false,
        },
      },
      colors: [props.color],
      xaxis: {
        crosshairs: {
          width: 1,
        },
      },
      yaxis: {
        min: 0,
        labels: {
          minWidth: 100,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="sparkLineThree" />
</template>
