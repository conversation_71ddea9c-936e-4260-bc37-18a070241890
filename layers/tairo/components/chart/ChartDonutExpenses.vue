<script setup lang="ts">
import type { DonutPieChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<DonutPieChartProps & {
  yAxisFormatter?: (value: any) => string
}>(), {
  height: 290,
  series: () => [1228, 423, 892, 629, 142],
  labels: () => ['Bills', 'Health', 'Education', 'Food', 'Other'],
  colors: () => ['var(--color-chart-base)', 'var(--color-violet-600)', 'var(--color-violet-700)', 'var(--color-violet-800)', 'var(--color-violet-900)'],
  title: '',
  animations: false,
  toolbar: false,
  legend: true,
  legendPosition: 'right',
  yAxisFormatter: (value: any) => formatPrice(value),
})

const demoDonut = reactive(useDemoDonut())

function useDemoDonut() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'donut',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      labels: props.labels,
      colors: props.colors,
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 280,
              toolbar: {
                show: props.toolbar,
              },
            },
            legend: {
              position: 'top',
            },
          },
        },
      ],
      legend: {
        show: props.legend,
        position: props.legendPosition,
        horizontalAlign: 'center',
      },
      tooltip: {
        y: {
          formatter: props.yAxisFormatter,
        },
      },
    },
  })
}
</script>

<template>
  <div class="relative">
    <LazyAddonApexcharts v-bind="demoDonut" />
  </div>
</template>
