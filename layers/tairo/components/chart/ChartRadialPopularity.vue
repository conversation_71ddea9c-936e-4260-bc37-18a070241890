<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 225,
  series: () => [67],
  labels: () => ['(30 days)'],
  colors: () => ['var(--color-chart-base)'],
  title: '',
  animations: false,
  toolbar: false,
  valueFormatter: (value: any) => `${value} %`,
})

const radialPopularity = reactive(useRadialPopularity())

function useRadialPopularity() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      chart: {
        toolbar: {
          show: props.toolbar,
        },
        animations: {
          enabled: props.animations,
        },
      },
      colors: props.colors,
      plotOptions: {
        radialBar: {
          startAngle: -135,
          endAngle: 135,
          dataLabels: {
            name: {
              fontSize: '13px',
              fontWeight: '600',
              color: 'var(--color-muted-400)',
              offsetY: 80,
            },
            value: {
              offsetY: 40,
              fontSize: '18px',
              fontFamily: 'var(--font-sans)',
              fontWeight: '500',
              color: undefined,
              formatter: props.valueFormatter,
            },
          },
        },
      },
      stroke: {
        dashArray: 4,
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="radialPopularity" />
</template>
