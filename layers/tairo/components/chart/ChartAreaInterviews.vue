<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 280,
  series: () => [
    {
      name: 'Interviews',
      data: [31, 40, 28, 51, 42, 109, 100],
    },
  ],
  categories: () => [
    '2020-09-19T00:00:00.000Z',
    '2020-09-20T01:30:00.000Z',
    '2020-09-21T02:30:00.000Z',
    '2020-09-22T03:30:00.000Z',
    '2020-09-23T04:30:00.000Z',
    '2020-09-24T05:30:00.000Z',
    '2020-09-25T06:30:00.000Z',
  ],
  colors: () => ['var(--color-chart-base)'],
  title: '',
  animations: false,
  toolbar: false,
})

const areaInterviews = reactive(useAreaInterviews())

function useAreaInterviews() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      title: {
        text: props.title || undefined,
        align: 'left',
      },
      legend: {
        position: 'top',
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'smooth',
      },
      xaxis: {
        type: 'datetime',
        categories: props.categories,
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm',
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.1,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="areaInterviews" />
</template>
