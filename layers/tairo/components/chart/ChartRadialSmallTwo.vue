<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 75,

  // Default series data - matches original implementation
  series: () => [53],

  // Labels
  labels: () => [''],

  // Color scheme
  colors: () => ['var(--color-success-500)'],

  // Chart title
  title: '',

  // Show value label
  showValue: false,

  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

const radialSmallTwo = reactive(useRadialSmallTwo())

function useRadialSmallTwo() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        offsetY: -10,
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      plotOptions: {
        radialBar: {
          hollow: {
            size: '50%',
          },
          dataLabels: {
            show: props.showValue,
          },
        },
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="radialSmallTwo" />
</template>
