<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  height: 280,
  series: () => [
    {
      name: 'Net Profit',
      data: [2134, 1932, 2141, 3455, 1242, 4212, 2342, 1983, 2421],
    },
    {
      name: 'Revenue',
      data: [8231, 7232, 9233, 8320, 7313, 8923, 9331, 8912, 9218],
    },
    {
      name: 'Free Cash Flow',
      data: [1523, 932, 2189, 1732, 1632, 1874, 1947, 2420, 2312],
    },
  ],
  categories: () => [
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
  ],
  colors: () => ['var(--color-chart-base)', 'var(--color-violet-200)', 'var(--color-violet-800)'],
  title: '',
  animations: false,
  toolbar: false,
  yAxisFormatter: (value: any) => formatPrice(value),
})

const demoBarMulti = reactive(useDemoBarMulti())

function useDemoBarMulti() {
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'bar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          // endingShape: 'rounded',
        },
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent'],
      },
      xaxis: {
        categories: props.categories,
      },
      yaxis: {
        title: {
          text: 'Amount',
        },
      },
      fill: {
        opacity: 1,
      },
      legend: {
        position: 'top',
        horizontalAlign: 'center',
      },
      title: {
        text: props.title,
        align: 'left',
      },
      tooltip: {
        y: {
          formatter: props.yAxisFormatter,
        },
      },
    },
  })
}
</script>

<template>
  <div class="relative">
    <LazyAddonApexcharts v-bind="demoBarMulti" />
  </div>
</template>
