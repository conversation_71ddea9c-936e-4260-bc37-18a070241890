<script setup lang="ts">
import type { CustomChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<CustomChartProps>(), {
  height: 295,

  // Default series data - matches original implementation
  series: () => [76],

  // Color scheme
  colors: () => ['var(--color-chart-base)'],

  // Chart title
  title: '',

  // Animation and toolbar settings
  animations: false,
  toolbar: false,

  // Custom options for gauge-specific configuration
  customOptions: () => ({
    chart: {
      sparkline: {
        enabled: true,
      },
    },
    plotOptions: {
      radialBar: {
        startAngle: -90,
        endAngle: 90,
        track: {
          background: '#e7e7e7',
          strokeWidth: '97%',
          margin: 5, // margin is in pixels
          dropShadow: {
            enabled: false,
            top: 2,
            left: 0,
            color: '#999',
            opacity: 1,
            blur: 2,
          },
        },
        dataLabels: {
          name: {
            show: false,
          },
          value: {
            offsetY: -2,
            fontSize: '22px',
          },
        },
      },
    },
    labels: ['Average Results'],
  }),
})

const demoRadialGaugeAlt = reactive(useDemoRadialGaugeAlt())

function useDemoRadialGaugeAlt() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
        ...props.customOptions?.chart,
      },
      colors: props.colors,
      plotOptions: props.customOptions?.plotOptions || {},
      labels: props.customOptions?.labels || [],
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoRadialGaugeAlt" />
</template>
