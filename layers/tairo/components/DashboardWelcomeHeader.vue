<script setup lang="ts">
export interface DashboardWelcomeHeaderProps {
  /**
   * The user's avatar image source
   */
  avatarSrc?: string
  /**
   * The user's name for the welcome message
   */
  userName?: string
  /**
   * The subtitle message below the welcome
   */
  subtitle?: string
  /**
   * Primary action button text
   */
  primaryButtonText?: string
  /**
   * Primary action button link
   */
  primaryButtonTo?: string
  /**
   * Secondary action button text
   */
  secondaryButtonText?: string
  /**
   * Secondary action button link
   */
  secondaryButtonTo?: string
  /**
   * Avatar size
   */
  avatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  /**
   * Whether to show action buttons
   */
  showButtons?: boolean
}

const props = withDefaults(defineProps<DashboardWelcomeHeaderProps>(), {
  avatarSrc: '/img/avatars/10.svg',
  userName: 'User',
  subtitle: 'Happy to see you again on your dashboard.',
  primaryButtonText: 'Primary Action',
  primaryButtonTo: '#',
  secondaryButtonText: 'Secondary Action',
  secondaryButtonTo: '#',
  avatarSize: 'lg',
  showButtons: true,
})
</script>

<template>
  <div class="mb-8 flex flex-col justify-between md:flex-row md:items-center">
    <div
      class="lg-landscape-max-w-full flex max-w-[425px] flex-col items-center gap-4 text-center md:flex-row md:text-start lg:max-w-full"
    >
      <BaseAvatar :src="props.avatarSrc" :size="props.avatarSize" />
      <div>
        <BaseHeading
          as="h2"
          size="xl"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>Welcome back, {{ props.userName }}</span>
        </BaseHeading>
        <BaseParagraph>
          <span class="text-muted-600 dark:text-muted-400">
            {{ props.subtitle }}
          </span>
        </BaseParagraph>
      </div>
    </div>
    <div
      v-if="props.showButtons"
      class="mt-4 flex items-center justify-center gap-2 md:mt-0 md:justify-start"
    >
      <BaseButton :to="props.secondaryButtonTo">
        <span>{{ props.secondaryButtonText }}</span>
      </BaseButton>
      <BaseButton variant="primary" :to="props.primaryButtonTo">
        <span>{{ props.primaryButtonText }}</span>
      </BaseButton>
    </div>
  </div>
</template>
