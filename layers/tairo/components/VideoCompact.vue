<script setup lang="ts">
export interface VideoCompactProps {
  /**
   * The border radius of the video thumbnail
   */
  rounded?: 'none' | 'md' | 'lg'
  /**
   * The video thumbnail image source
   */
  imageSrc?: string
  /**
   * The alt text for the video thumbnail
   */
  imageAlt?: string
  /**
   * The video category/tag
   */
  category?: string
  /**
   * The video title
   */
  title?: string
  /**
   * The number of views
   */
  views?: string | number
  /**
   * The play icon name
   */
  playIcon?: string
  /**
   * The views icon name
   */
  viewsIcon?: string
  /**
   * Whether to show the views count
   */
  showViews?: boolean
  /**
   * Whether to show the category
   */
  showCategory?: boolean
}

const props = withDefaults(defineProps<VideoCompactProps>(), {
  rounded: 'md',
  imageSrc: '/img/illustrations/dashboards/hobbies/hobby-3.svg',
  imageAlt: 'Video thumbnail',
  category: 'Best Movies',
  title: 'The man who didn\'t want to talk to horses',
  views: '3,862 views',
  playIcon: 'ic:round-play-arrow',
  viewsIcon: 'lucide:eye',
  showViews: true,
  showCategory: true,
})
</script>

<template>
  <div class="group flex w-full gap-4">
    <div class="relative shrink-0">
      <div
        class="h-24 w-20 overflow-hidden" :class="[
          props.rounded === 'md' ? 'rounded-md' : '',
          props.rounded === 'lg' ? 'rounded-lg' : '',
        ]"
      >
        <TairoImageZoom
          :src="props.imageSrc"
          :alt="props.imageAlt"
          class="object-cover object-top" :class="[
            props.rounded === 'md' ? 'rounded-md' : '',
            props.rounded === 'lg' ? 'rounded-lg' : '',
          ]"
        />
      </div>
      <div class="pointer-events-none absolute start-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
        <span
          class="border-primary-500 bg-primary-500 dark:group-hover:bg-muted-800 flex size-10 items-center justify-center rounded-full border-2 transition-colors duration-300 group-hover:bg-white"
        >
          <Icon
            :name="props.playIcon"
            class="group-hover:text-primary-500 size-5 text-white"
          />
        </span>
      </div>
    </div>
    <div class="flex flex-col">
      <span
        v-if="props.showCategory"
        class="text-primary-500 my-1 font-sans text-xs uppercase"
      >
        {{ props.category }}
      </span>
      <BaseHeading
        as="h3"
        size="sm"
        weight="medium"
        lead="tight"
      >
        <span>{{ props.title }}</span>
      </BaseHeading>
      <div
        v-if="props.showViews"
        class="text-muted-400 mt-auto flex items-center gap-2 font-sans text-xs"
      >
        <Icon :name="props.viewsIcon" class="size-4" />
        <span>{{ props.views }}</span>
      </div>
    </div>
  </div>
</template>
