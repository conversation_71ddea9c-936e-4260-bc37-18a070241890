<script setup lang="ts">
export interface User {
  id: string | number
  picture: string
  name: string
  position: string
  progress: number
}

export interface UserListProps {
  /**
   * The border radius for avatars and buttons
   */
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'
  /**
   * Array of users to display
   */
  users?: User[]
  /**
   * Avatar size
   */
  avatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /**
   * Whether to show progress percentages
   */
  showProgress?: boolean
  /**
   * Whether to show action buttons
   */
  showActions?: boolean
  /**
   * Action button icon
   */
  actionIcon?: string
  /**
   * Action button link/route
   */
  actionTo?: string
  /**
   * Custom spacing between items
   */
  spacing?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<UserListProps>(), {
  rounded: 'sm',
  avatarSize: 'sm',
  showProgress: true,
  showActions: true,
  actionIcon: 'lucide:chevron-right',
  actionTo: '#',
  spacing: 'md',
  users: () => [
    {
      id: 0,
      picture: '/img/avatars/16.svg',
      name: '<PERSON>',
      position: 'Business Analyst',
      progress: 18,
    },
    {
      id: 1,
      picture: '/img/avatars/10.svg',
      name: '<PERSON> <PERSON>',
      position: 'Project Manager',
      progress: 22,
    },
    {
      id: 2,
      picture: '/img/avatars/6.svg',
      name: 'John Baxter',
      position: 'Product Manager',
      progress: -12,
    },
    {
      id: 3,
      picture: '/img/avatars/12.svg',
      name: 'Amelia Shepherd',
      position: 'Product Manager',
      progress: 32,
    },
    {
      id: 4,
      picture: '/img/avatars/11.svg',
      name: 'Daryl Zanuk',
      position: 'Mobile Developer',
      progress: -4,
    },
    {
      id: 5,
      picture: '/img/avatars/5.svg',
      name: 'Clarissa Miller',
      position: 'UI/UX Designer',
      progress: 32,
    },
  ],
})

const spacingClasses = computed(() => {
  const spacings = {
    sm: 'space-y-4',
    md: 'space-y-6',
    lg: 'space-y-8',
  }
  return spacings[props.spacing] || spacings.md
})
</script>

<template>
  <div :class="spacingClasses">
    <div
      v-for="user in props.users"
      :key="user.id"
      class="flex items-center gap-2"
    >
      <BaseAvatar
        :src="user.picture"
        :size="props.avatarSize"
        :rounded="props.rounded"
      />
      <div>
        <BaseHeading
          as="h3"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-muted-100"
        >
          <span>{{ user.name }}</span>
        </BaseHeading>
        <BaseText size="xs" class="text-muted-600 dark:text-muted-400">
          <span>{{ user.position }}</span>
        </BaseText>
      </div>
      <div class="ms-auto flex items-center justify-end gap-4">
        <BaseParagraph
          v-if="props.showProgress"
          size="sm"
          weight="semibold"
          :class="user.progress > 0 ? 'text-success-500' : 'text-destructive-500'"
        >
          <span>{{ user.progress }}%</span>
        </BaseParagraph>
        <BaseButton
          v-if="props.showActions"
          :to="props.actionTo"
          variant="muted"
          size="icon-sm"
          :rounded="props.rounded"
        >
          <Icon :name="props.actionIcon" class="size-4" />
        </BaseButton>
      </div>
    </div>
  </div>
</template>
