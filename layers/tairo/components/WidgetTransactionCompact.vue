<script setup lang="ts">
export interface Transaction {
  id: string | number
  date: string
  issuer: string
  amount: number
  status: 'complete' | 'in progress' | 'processing' | 'cancelled'
}

export interface WidgetTransactionCompactProps {
  /**
   * The widget title
   */
  title?: string
  /**
   * The link text for "View all"
   */
  linkText?: string
  /**
   * The link URL for "View all"
   */
  linkTo?: string
  /**
   * Array of transactions to display
   */
  transactions?: Transaction[]
  /**
   * Loading state
   */
  loading?: boolean
  /**
   * Empty state title
   */
  emptyTitle?: string
  /**
   * Empty state subtitle
   */
  emptySubtitle?: string
  /**
   * Whether to show the link
   */
  showLink?: boolean
  /**
   * Currency symbol
   */
  currency?: string
}

const props = withDefaults(defineProps<WidgetTransactionCompactProps>(), {
  title: 'Recent Transactions',
  linkText: 'View all',
  linkTo: '#',
  loading: false,
  emptyTitle: 'No matching results',
  emptySubtitle: 'Looks like we couldn\'t find any matching results for your search terms. Try other search terms.',
  showLink: true,
  currency: '$',
  transactions: () => [
    {
      id: 1,
      date: 'Oct 15',
      issuer: 'Amazon Purchase',
      amount: -89.99,
      status: 'complete',
    },
    {
      id: 2,
      date: 'Oct 14',
      issuer: 'Salary Deposit',
      amount: 2500.00,
      status: 'complete',
    },
    {
      id: 3,
      date: 'Oct 13',
      issuer: 'Netflix Subscription',
      amount: -15.99,
      status: 'processing',
    },
  ],
})

function statusColor(itemStatus: string) {
  switch (itemStatus) {
    case 'complete':
      return 'dark'
    case 'in progress':
      return 'primary'
    case 'processing':
      return 'default'
    case 'cancelled':
      return 'muted'
    default:
      return 'default'
  }
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="py-4 px-2 md:py-6 md:px-4"
  >
    <div class="px-2 items-center justify-between sm:flex">
      <BaseHeading
        as="h4"
        size="xs"
        weight="medium"
        lead="none"
        class="text-muted-700 dark:text-muted-100 uppercase"
      >
        {{ props.title }}
      </BaseHeading>
      <LinkArrow
        v-if="props.showLink"
        :to="props.linkTo"
        :label="props.linkText"
      />
    </div>
    <div v-if="!props.loading && props.transactions.length === 0" class="px-2">
      <BasePlaceholderPage
        :title="props.emptyTitle"
        :subtitle="props.emptySubtitle"
      />
    </div>
    <div v-else class="mt-7 overflow-x-auto">
      <table class="w-full whitespace-nowrap">
        <thead>
          <tr>
            <th class="w-1/5" />
            <th class="w-2/5" />
            <th />
            <th />
            <th />
            <th />
          </tr>
        </thead>
        <tbody>
          <!-- Row -->
          <tr
            v-for="item in props.transactions"
            :key="item.id"
            tabindex="0"
            class="hover:bg-muted-200/50 dark:hover:bg-muted-900/50 transition-colors duration-100"
          >
            <td class="py-2 ps-2 rounded-s-md">
              <BaseText
                size="sm"
                weight="medium"
                lead="none"
                class="text-muted-400"
              >
                {{ item.date }}
              </BaseText>
            </td>
            <td class="py-2">
              <BaseText
                size="sm"
                weight="medium"
                lead="none"
                class="text-muted-600 dark:text-muted-300"
              >
                {{ item.issuer }}
              </BaseText>
            </td>
            <td class="px-4 py-2 text-end">
              <BaseText
                size="sm"
                weight="semibold"
                lead="none"
                class="text-muted-800 dark:text-muted-100" :class="[
                  item.amount < 0 ? 'text-danger-600 dark:text-danger-400' : 'text-success-600 dark:text-success-400',
                ]"
              >
                {{ props.currency }}{{ Math.abs(item.amount).toLocaleString() }}
              </BaseText>
            </td>
            <td class="ps-4 pe-2 py-2 rounded-e-md text-end">
              <BaseTag
                :variant="statusColor(item.status)"
                rounded="full"
                size="sm"
              >
                {{ item.status }}
              </BaseTag>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BaseCard>
</template>
