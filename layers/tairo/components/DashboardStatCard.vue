<script setup lang="ts">
export interface DashboardStatCardProps {
  /**
   * The icon name to display
   */
  icon?: string
  /**
   * The main value/number to display
   */
  value?: string | number
  /**
   * The description/label for the stat
   */
  label?: string
  /**
   * The color variant for the icon
   */
  color?: 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'amber' | 'green' | 'indigo'
  /**
   * The icon size
   */
  iconSize?: 'xs' | 'sm' | 'md' | 'lg'
  /**
   * Whether to show a border ring
   */
  showRing?: boolean
  /**
   * Custom CSS classes for the icon container
   */
  iconClasses?: string
}

const props = withDefaults(defineProps<DashboardStatCardProps>(), {
  icon: 'ph:chart-line-duotone',
  value: '0',
  label: 'Stat Label',
  color: 'primary',
  iconSize: 'sm',
  showRing: true,
  iconClasses: '',
})

const colorClasses = computed(() => {
  const colors = {
    primary: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2',
    info: 'bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400 dark:border-info-500 dark:border-2',
    success: 'bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400 dark:border-success-500 dark:border-2',
    warning: 'bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400 dark:border-warning-500 dark:border-2',
    danger: 'bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400 dark:border-danger-500 dark:border-2',
    amber: 'bg-amber-100 text-amber-500 dark:border-2 dark:border-amber-500 dark:bg-amber-500/20 dark:text-amber-400',
    green: 'bg-green-100 text-green-500 dark:border-2 dark:border-green-500 dark:bg-green-500/20 dark:text-green-400',
    indigo: 'bg-indigo-100 text-indigo-500 dark:border-2 dark:border-indigo-500 dark:bg-indigo-500/20 dark:text-indigo-400',
  }
  return colors[props.color] || colors.primary
})

const containerClasses = computed(() => {
  const baseClasses = 'flex flex-col items-center text-center shadow-sm gap-2 rounded-md p-4'
  const ringClasses = props.showRing ? 'ring-muted-900/5 dark:ring-muted-800 ring-1' : ''
  return `${baseClasses} ${ringClasses}`.trim()
})
</script>

<template>
  <div :class="containerClasses">
    <BaseIconBox
      :size="props.iconSize"
      :class="[colorClasses, props.iconClasses]"
      rounded="full"
      variant="none"
    >
      <Icon :name="props.icon" class="size-5" />
    </BaseIconBox>
    <div>
      <BaseHeading
        as="h2"
        size="md"
        weight="semibold"
        lead="tight"
        class="text-muted-800 dark:text-white"
      >
        <span>{{ props.value }}</span>
      </BaseHeading>
      <BaseParagraph size="sm">
        <span class="text-muted-500 dark:text-muted-400">
          {{ props.label }}
        </span>
      </BaseParagraph>
    </div>
  </div>
</template>
