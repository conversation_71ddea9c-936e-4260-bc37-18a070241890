/**
 * Common type definitions for all chart components
 * These interfaces provide a consistent API for dynamic data across all chart types
 */

import type { ApexOptions } from 'apexcharts'

/**
 * Base series data structure for simple charts
 */
export interface ChartSeriesData {
  name: string
  data: number[]
}

/**
 * Extended series data for charts with x,y coordinates
 */
export interface ChartSeriesXY {
  name: string
  data: Array<{
    x: string | number | Date
    y: number | number[]
  }>
}

/**
 * Series data for bubble charts
 */
export interface ChartSeriesBubble {
  name: string
  data: Array<{
    x: number
    y: number
    z: number
  }>
}

/**
 * Base props interface for all chart components
 */
export interface BaseChartProps {
  /**
   * Chart height in pixels
   */
  height?: number

  /**
   * Chart title
   */
  title?: string

  /**
   * Custom colors array
   */
  colors?: string[]

  /**
   * Enable/disable animations
   */
  animations?: boolean

  /**
   * Enable/disable toolbar
   */
  toolbar?: boolean
}

/**
 * Props for simple single-series charts (Area, Bar, Line)
 */
export interface SimpleSingleSeriesChartProps extends BaseChartProps {
  /**
   * Chart data series
   */
  series?: ChartSeriesData[]

  /**
   * X-axis categories/labels
   */
  categories?: string[]

  /**
   * Y-axis formatter function
   */
  yAxisFormatter?: (value: any) => string
}

/**
 * Props for multi-series charts
 */
export interface MultiSeriesChartProps extends BaseChartProps {
  /**
   * Multiple data series
   */
  series?: ChartSeriesData[]

  /**
   * X-axis categories/labels
   */
  categories?: string[]

  /**
   * Enable stacking
   */
  stacked?: boolean

  /**
   * Y-axis formatter function
   */
  yAxisFormatter?: (value: any) => string
}

/**
 * Props for donut and pie charts
 */
export interface DonutPieChartProps extends BaseChartProps {
  /**
   * Chart data values
   */
  series?: number[]

  /**
   * Labels for each slice
   */
  labels?: string[]

  /**
   * Enable/disable legend
   */
  legend?: boolean

  /**
   * Legend position
   */
  legendPosition?: 'top' | 'right' | 'bottom' | 'left'
}

/**
 * Props for radial and gauge charts
 */
export interface RadialChartProps extends BaseChartProps {
  /**
   * Chart data values (percentages)
   */
  series?: number[]

  /**
   * Labels for each value
   */
  labels?: string[]

  /**
   * Track background colors
   */
  trackColors?: string[]

  /**
   * Show/hide value label
   */
  showValue?: boolean
}

/**
 * Props for scatter and bubble charts
 */
export interface ScatterBubbleChartProps extends BaseChartProps {
  /**
   * Chart data series with x,y,z coordinates
   */
  series?: ChartSeriesBubble[] | ChartSeriesXY[]

  /**
   * X-axis configuration
   */
  xAxis?: {
    min?: number
    max?: number
    title?: string
  }

  /**
   * Y-axis configuration
   */
  yAxis?: {
    min?: number
    max?: number
    title?: string
  }
}

/**
 * Props for radar charts
 */
export interface RadarChartProps extends BaseChartProps {
  /**
   * Chart data series
   */
  series?: ChartSeriesData[]

  /**
   * Categories for radar points
   */
  categories?: string[]

  /**
   * Fill opacity
   */
  fillOpacity?: number
}

/**
 * Props for timeline charts
 */
export interface TimelineChartProps extends BaseChartProps {
  /**
   * Timeline data series
   */
  series?: Array<{
    name: string
    data: Array<{
      x: string
      y: [number, number]
      fillColor?: string
    }>
  }>
}

/**
 * Props for spark charts (small inline charts)
 */
export interface SparkChartProps {
  /**
   * Chart data
   */
  data?: number[]

  /**
   * Chart width
   */
  width?: number

  /**
   * Chart height
   */
  height?: number

  /**
   * Line/bar color
   */
  color?: string

  /**
   * Chart type
   */
  type?: 'line' | 'area' | 'bar'
}

/**
 * Generic chart props that accepts custom options
 */
export interface CustomChartProps extends BaseChartProps {
  /**
   * Chart data series (flexible format)
   */
  series?: any[]

  /**
   * Custom ApexCharts options override
   */
  customOptions?: Partial<ApexOptions>
}
