export default defineNuxtConfig({
  $meta: {
    name: '@pib/auth-module',
    description: 'Authentication module providing login, logout, and user management with full system integration',
    version: '1.0.0',
  },

  // Module-specific configuration
  runtimeConfig: {
    public: {
      firebase: {
        apiKey: process.env.FIREBASE_API_KEY || '',
        authDomain: process.env.FIREBASE_AUTH_DOMAIN || '',
        projectId: process.env.FIREBASE_PROJECT_ID || '',
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET || '',
        messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
        appId: process.env.FIREBASE_APP_ID || '',
        measurementId: process.env.FIREBASE_MEASUREMENT_ID || '',
        useEmulator: process.env.FIREBASE_USE_EMULATOR === 'true',
      },
      authModule: {
        firebase: {
          enabled: true,
        },
        persistence: {
          enabled: true,
          ttl: 24 * 60 * 60 * 1000, // 24 hours
        },
        performance: {
          batchingEnabled: true,
          cacheEnabled: true,
          cacheTTL: 5 * 60 * 1000, // 5 minutes
        },
        errorHandling: {
          logErrors: true,
          reportErrors: false,
        },
      },
    },
  },

  // Auto-import components and composables from this module
  components: {
    dirs: [
      {
        path: './components',
        pathPrefix: false,
      },
    ],
  },

  // Auto-import composables and utilities
  imports: {
    dirs: ['./composables', './utils'],
  },

  // Module CSS
  css: [
    // './assets/css/main.css',
  ],

  // Module dependencies
  modules: [
    // Add module-specific dependencies here
  ],

  // Module build configuration
  build: {
    transpile: [
      // Add packages that need transpilation
      'firebase',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'firebase-admin',
      'firebase-admin/app',
      'firebase-admin/auth',
      'firebase-admin/firestore',
      '@google-cloud/firestore',
    ],
  },

  // Nitro configuration for server-side
  nitro: {
    moduleSideEffects: ['firebase/app', 'firebase/auth', 'firebase/firestore', 'firebase-admin', '@google-cloud/firestore'],
    esbuild: {
      options: {
        target: 'esnext',
      },
    },
    experimental: {
      wasm: true,
    },
  },

  // TypeScript configuration
  typescript: {
    includeWorkspace: true,
  },

  // Development tools configuration
  devtools: {
    enabled: true,
  },

  // Hooks for module lifecycle
  hooks: {
    'modules:before': () => {
      console.info('[Auth Module] Loading module...')
    },
    'modules:done': () => {
      console.info('[Auth Module] Module loaded successfully')
    },
  },
})
