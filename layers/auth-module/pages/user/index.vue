<script setup lang="ts">
definePageMeta({
  title: 'Preferences',
  preview: {
    title: 'Preferences - Profile',
    description: 'For account management',
    categories: ['layouts', 'settings'],
    src: '/img/screens/layouts-preferences-profile.png',
    srcDark: '/img/screens/layouts-preferences-profile-dark.png',
    order: 84,
    new: true,
  },
})

// Use auth composables to get real user data
const { currentProfile, profileLoading, profileError, updateProfile } = useCurrentProfile()
const { user } = useAuth()

// Create reactive form with real user data
const form = reactive({
  username: '',
  firstName: '',
  lastName: '',
  email: '',
  jobTitle: '',
  country: '',
  website: '',
  bio: '',
  showProfile: true,
})

// Loading and error states
const isSubmitting = ref(false)
const toaster = useNuiToasts()

// Watch for profile data and populate form
watchEffect(() => {
  if (currentProfile.value) {
    const profile = currentProfile.value
    form.username = profile.username || profile.displayName || ''
    form.firstName = profile.firstName || profile.displayName?.split(' ')[0] || ''
    form.lastName = profile.lastName || profile.displayName?.split(' ').slice(1).join(' ') || ''
    form.email = user.value?.email || ''
    form.jobTitle = profile.title || profile.role || ''
    form.country = profile.location || ''
    form.website = profile.website || ''
    form.bio = profile.shortBio || profile.longBio || ''
    form.showProfile = profile.settings?.showProfile ?? true
  }
})

// Form submission handler
const handleSubmit = async () => {
  if (!currentProfile.value) return

  isSubmitting.value = true

  try {
    // Prepare update data
    const updateData = {
      firstName: form.firstName,
      lastName: form.lastName,
      title: form.jobTitle,
      location: form.country,
      website: form.website,
      shortBio: form.bio,
      displayName: `${form.firstName} ${form.lastName}`.trim(),
      settings: {
        ...currentProfile.value.settings,
        showProfile: form.showProfile,
      },
      updatedAt: new Date().toISOString(),
    }

    await updateProfile(updateData)

    toaster.add({
      message: 'Profile updated successfully!',
      color: 'success',
      icon: 'heroicons:check-circle',
    })
  } catch (error: any) {
    console.error('Profile update error:', error)
    toaster.add({
      message: error.message || 'Failed to update profile',
      color: 'danger',
      icon: 'heroicons:x-circle',
    })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <div class="dark:[--color-input-default-bg:var(--color-muted-950)]">
    <!-- Loading state -->
    <div v-if="profileLoading" class="flex items-center justify-center py-12">
      <BaseSpinner size="lg" />
    </div>

    <!-- Error state -->
    <div v-else-if="profileError" class="mt-6">
      <BaseMessage type="danger">
        Failed to load profile data. Please try refreshing the page.
      </BaseMessage>
    </div>

    <!-- Main content -->
    <div v-else>
      <div class="mt-6 space-y-1">
        <BaseParagraph
          size="md"
          weight="medium"
          class="text-muted-900 dark:text-muted-200"
        >
          Personal info
        </BaseParagraph>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          Update your personal information and profile settings.
        </BaseParagraph>
      </div>

      <form
        @submit.prevent="handleSubmit"
        class="mt-12 max-w-3xl"
      >
      <div class="space-y-8">
        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> Profile Photo </label>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <div class="flex items-center space-x-6">
              <BaseAvatar
                :src="currentProfile?.avatarUrl || '/img/avatars/10.svg'"
                size="md"
                rounded="none"
                mask="blob"
              />
              <BaseButton rounded="md" size="sm">
                Remove
              </BaseButton>
              <BaseButton
                rounded="md"
                size="sm"
                variant="primary"
              >
                Update
              </BaseButton>
            </div>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> First & Last Name </label>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <div class="grid grid-cols-1 gap-x-4 gap-y-5 sm:grid-cols-2">
              <BaseInput
                v-model="form.firstName"
                type="text"
                placeholder="First Name"
                rounded="md"
                size="lg"
              />

              <BaseInput
                v-model="form.lastName"
                type="text"
                placeholder="Last Name"
                rounded="md"
                size="lg"
              />
            </div>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> Email Address </label>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <BaseInput
              v-model="form.email"
              type="text"
              placeholder="Email Address"
              rounded="md"
              size="lg"
            />
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> Write Your Bio </label>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <BaseTextarea
              v-model="form.bio"
              rounded="md"
              placeholder="Short bio..."
            />
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <div class="sm:mt-px sm:pt-2">
            <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> Username </label>
            <BaseParagraph
              size="xs"
              weight="medium"
              class="text-muted-400"
            >
              You can change it later
            </BaseParagraph>
          </div>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <div class="flex items-center focus-within:nui-focus rounded-md  *:first:border-e-0 *:first:rounded-e-none *:last:rounded-s-none">
              <div class="border h-12 px-4 flex items-center justify-center rounded-md bg-input-muted-bg border-input-muted-border">
                <span class="text-sm font-medium text-input-muted-text/60">https://tairo.io/users/</span>
              </div>
              <BaseInput
                v-model="form.username"
                type="text"
                placeholder="Username"
                rounded="md"
                size="lg"
                class="ring-0!"
              />
            </div>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> Website </label>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <div class="flex items-center focus-within:nui-focus rounded-md  *:first:border-e-0 *:first:rounded-e-none *:last:rounded-s-none">
              <div class="border h-12 px-4 flex items-center justify-center rounded-md bg-input-muted-bg border-input-muted-border">
                <span class="text-sm font-medium text-input-muted-text/60">https://</span>
              </div>
              <BaseInput
                v-model="form.website"
                type="text"
                placeholder="Username"
                rounded="md"
                size="lg"
                class="ring-0!"
              />
            </div>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> Job Title </label>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <BaseInput
              v-model="form.jobTitle"
              type="text"
              placeholder="Job Title"
              rounded="md"
              size="lg"
            />

            <div class="relative mt-2 flex items-center">
              <BaseCheckbox
                v-model="form.showProfile"
                label="Show this on my profile "
              />
            </div>
          </div>
        </div>

        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-5">
          <label class="text-muted-900 dark:text-muted-100 block font-sans text-sm font-medium sm:mt-px sm:pt-2"> Country </label>
          <div class="mt-2 sm:col-span-2 sm:mt-0">
            <BaseSelect
              v-model="form.country"
              size="lg"
              rounded="md"
              placeholder="Select a country"
            >
              <BaseSelectItem value="United States">
                United States
              </BaseSelectItem>
              <BaseSelectItem value="Canada">
                Canada
              </BaseSelectItem>
              <BaseSelectItem value="United Kingdom">
                United Kingdom
              </BaseSelectItem>
              <BaseSelectItem value="France">
                France
              </BaseSelectItem>
              <BaseSelectItem value="China">
                China
              </BaseSelectItem>
            </BaseSelect>
          </div>
        </div>
      </div>

        <div class="mt-6 sm:mt-12 flex justify-end">
          <BaseButton
            type="submit"
            rounded="md"
            variant="primary"
            class="w-full md:w-40"
            :disabled="isSubmitting || profileLoading"
            :loading="isSubmitting"
          >
            Update Profile
          </BaseButton>
        </div>
      </form>
    </div>
  </div>
</template>
