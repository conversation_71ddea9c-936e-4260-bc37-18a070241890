<script setup lang="ts">
definePageMeta({
  title: 'Workspaces',
  preview: {
    title: 'Preferences - Workspaces',
    description: 'For account management',
    categories: ['layouts', 'settings'],
    src: '/img/screens/layouts-preferences-workspaces.png',
    srcDark: '/img/screens/layouts-preferences-workspaces-dark.png',
    order: 85,
    new: true,
  },
})

// Use auth composables to get real workspace data
const { workspaces, currentWorkspace, setCurrentWorkspace, user } = useAuth()
const toaster = useNuiToasts()

// Track selected workspace for UI
const selectedWorkspaceId = ref(currentWorkspace.value?.id || '')

// Watch for current workspace changes
watchEffect(() => {
  if (currentWorkspace.value?.id) {
    selectedWorkspaceId.value = currentWorkspace.value.id
  }
})

// Loading states
const isLoading = ref(false)
const isSwitching = ref(false)

// Handle workspace selection/switching
const handleWorkspaceSelect = async (workspaceId: string) => {
  if (workspaceId === selectedWorkspaceId.value) return

  isSwitching.value = true
  try {
    await setCurrentWorkspace(workspaceId)
    selectedWorkspaceId.value = workspaceId

    toaster.add({
      message: 'Workspace switched successfully!',
      color: 'success',
      icon: 'heroicons:check-circle',
    })
  } catch (error: any) {
    console.error('Workspace switch error:', error)
    toaster.add({
      message: error.message || 'Failed to switch workspace',
      color: 'danger',
      icon: 'heroicons:x-circle',
    })
  } finally {
    isSwitching.value = false
  }
}

// Handle new workspace creation (placeholder for now)
const handleNewWorkspace = () => {
  toaster.add({
    message: 'New workspace creation coming soon!',
    color: 'info',
    icon: 'heroicons:information-circle',
  })
}

// Handle new member invitation (placeholder for now)
const handleNewMember = () => {
  toaster.add({
    message: 'Member invitation coming soon!',
    color: 'info',
    icon: 'heroicons:information-circle',
  })
}

// Get current workspace members
const currentWorkspaceMembers = computed(() => {
  if (!currentWorkspace.value?.members) return []

  return Object.values(currentWorkspace.value.members).map(member => ({
    name: member.displayName || member.email,
    email: member.email,
    img: member.avatarUrl || '/img/avatars/placeholder.svg',
    role: member.role,
  }))
})
</script>

<template>
  <div class="mt-8 space-y-8">
    <BaseCard rounded="lg" class="overflow-hidden">
      <div class="px-4 py-5 sm:p-6">
        <div class="sm:flex sm:items-center sm:justify-between">
          <div class="space-y-1">
            <BaseHeading
              as="h4"
              size="md"
              weight="medium"
              class="text-muted-900 text-base font-bold dark:text-white"
            >
              Workspaces you are on
            </BaseHeading>
            <BaseParagraph
              size="sm"
              class="text-muted-500 dark:text-muted-400"
            >
              Lorem ipsum dolor sit amet, consectetur adipis.
            </BaseParagraph>
          </div>

          <div class="mt-4 sm:mt-0">
            <BaseButton
              rounded="md"
              class="font-medium"
              @click="handleNewWorkspace"
            >
              <Icon name="lucide:plus" class="size-4" />
              <span>New Workspace</span>
            </BaseButton>
          </div>
        </div>

        <div class="mt-8 flow-root">
          <div v-if="!workspaces || workspaces.length === 0" class="text-center py-8">
            <BaseParagraph class="text-muted-500 dark:text-muted-400">
              No workspaces found. Create your first workspace to get started.
            </BaseParagraph>
          </div>
          <div v-else class="divide-muted-100 dark:divide-muted-800/80 -my-5 divide-y">
            <div
              v-for="workspace in workspaces"
              :key="workspace.id"
              class="cursor-pointer p-4 transition-colors duration-300"
              :class="isSwitching ? 'opacity-50 pointer-events-none' : ''"
              role="button"
              tabindex="0"
              @click="handleWorkspaceSelect(workspace.id!)"
            >
              <div class="flex items-center gap-4">
                <div class="relative">
                  <BaseAvatar
                    :src="workspace.logo || '/img/icons/logos/placeholder.svg'"
                    size="sm"
                  />
                </div>
                <div>
                  <BaseParagraph
                    size="sm"
                    weight="medium"
                    class="text-muted-900 dark:text-muted-100"
                  >
                    {{ workspace.name }}
                  </BaseParagraph>
                  <BaseParagraph
                    size="sm"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ workspace.memberCount || 0 }} members
                  </BaseParagraph>
                </div>
                <div v-if="selectedWorkspaceId === workspace.id">
                  <BaseTag
                    variant="primary"
                    size="sm"
                    rounded="full"
                  >
                    Current
                  </BaseTag>
                </div>

                <div class="ms-auto flex items-center gap-2">
                  <BaseButton
                    v-if="selectedWorkspaceId === workspace.id"
                    class="text-primary-500! font-medium"
                    rounded="md"
                    size="sm"
                  >
                    Edit
                  </BaseButton>
                  <BaseButton
                    v-if="workspace.role !== 'owner'"
                    rounded="md"
                    size="sm"
                    variant="muted"
                  >
                    Leave
                  </BaseButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseCard>

    <BaseCard rounded="lg" class="overflow-hidden">
      <div class="px-4 py-5 sm:p-6">
        <div class="sm:flex sm:items-center sm:justify-between">
          <div class="space-y-1">
            <BaseHeading
              as="h4"
              size="md"
              weight="medium"
              class="text-muted-900 text-base dark:text-white"
            >
              Workspace members
            </BaseHeading>
            <BaseParagraph
              size="sm"
              class="text-muted-500 dark:text-muted-400"
            >
              Lorem ipsum dolor sit amet, consectetur adipis.
            </BaseParagraph>
          </div>

          <div class="mt-4 sm:mt-0">
            <BaseButton
              rounded="md"
              class="font-medium"
              @click="handleNewMember"
            >
              <Icon name="lucide:plus" class="size-4" />
              <span>New Member</span>
            </BaseButton>
          </div>
        </div>

        <div class="mt-8 flow-root">
          <div v-if="!currentWorkspaceMembers || currentWorkspaceMembers.length === 0">
            <BasePlaceholderPage
              title="Nothing to show"
              subtitle="There are no members to show in this workspace. Start inviting people to join."
            >
              <div class="mt-4 flex justify-center gap-2">
                <BaseButton
                  rounded="md"
                  class="font-medium"
                  @click="handleNewMember"
                >
                  <Icon name="lucide:plus" class="size-4" />
                  <span>Invite New Member</span>
                </BaseButton>
              </div>
            </BasePlaceholderPage>
          </div>
          <div v-else class="divide-muted-100 dark:divide-muted-800/80 -my-5 divide-y">
            <div
              v-for="member in currentWorkspaceMembers"
              :key="member.email"
              class="p-4"
            >
              <div class="flex items-center gap-4">
                <div class="relative">
                  <BaseAvatar :src="member.img" size="sm" />
                </div>
                <div>
                  <BaseParagraph
                    size="sm"
                    weight="medium"
                    class="text-muted-900 dark:text-muted-100"
                  >
                    {{ member.name }}
                  </BaseParagraph>
                  <BaseParagraph
                    size="sm"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ member.email }}
                  </BaseParagraph>
                  <BaseParagraph
                    size="xs"
                    class="text-primary-500 capitalize"
                  >
                    {{ member.role }}
                  </BaseParagraph>
                </div>

                <div class="ms-auto flex items-center gap-2">
                  <BaseButton
                    class="text-primary-500! font-medium"
                    rounded="md"
                    size="sm"
                  >
                    Edit
                  </BaseButton>
                  <BaseButton
                    v-if="member.role !== 'owner'"
                    rounded="md"
                    size="sm"
                    variant="muted"
                  >
                    Remove
                  </BaseButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseCard>
  </div>
</template>
