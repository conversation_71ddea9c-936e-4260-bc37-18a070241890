<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useFieldError, useForm } from 'vee-validate'
import { z } from 'zod'
import type { Profile, SocialLink } from '~/layers/auth-module/types/profile'
import { useProfiles<PERSON><PERSON> } from '~/layers/auth-module/composables/useDataApi'
import { useAuth } from '~/layers/auth-module/composables/auth'
import { useCurrentProfile } from '~/layers/auth-module/composables/useProfile'

definePageMeta({
  title: 'Edit Profile',
  preview: {
    title: 'Edit profile 1',
    description: 'For editing a user profile',
    categories: ['layouts', 'profile', 'forms'],
    src: '/img/screens/layouts-subpages-profile-edit-1.png',
    srcDark: '/img/screens/layouts-subpages-profile-edit-1-dark.png',
    order: 76,
  },
  pageTransition: {
    enterActiveClass: 'transition-all duration-500 ease-out',
    enterFromClass: 'translate-y-20 opacity-0',
    enterToClass: 'translate-y-0 opacity-100',
    leaveActiveClass: 'transition-all duration-200 ease-in',
    leaveFromClass: 'translate-y-0 opacity-100',
    leaveToClass: 'translate-y-20 opacity-0',
  },
})

// This is the object that will contain the validation messages
const ONE_MB = 1000000
const VALIDATION_TEXT = {
  FIRST_REQUIRED: 'Your first name can\'t be empty',
  LASTNAME_REQUIRED: 'Your last name can\'t be empty',
  OPTION_REQUIRED: 'Please select an option',
  AVATAR_TOO_BIG: `Avatar size must be less than 1MB`,
}

// This is the Zod schema for the form input
// It's used to define the shape that the form data will have
const zodSchema = z
  .object({
    avatar: z.custom<File>(v => v instanceof File).nullable(),
    profile: z.object({
      firstName: z.string().min(1, VALIDATION_TEXT.FIRST_REQUIRED),
      lastName: z.string().min(1, VALIDATION_TEXT.LASTNAME_REQUIRED),
      role: z.string().optional(),
      location: z.string(),
      bio: z.string(),
    }),
    info: z.object({
      experience: z
        .union([
          z.literal('0-2 years'),
          z.literal('2-5 years'),
          z.literal('5-10 years'),
          z.literal('10+ years'),
        ])
        .nullable(),
      firstJob: z.string().nullable(),
      flexible: z.string().nullable(),
      remote: z.string().nullable(),
    }),
    social: z.object({
      facebook: z.string(),
      twitter: z.string(),
      dribbble: z.string(),
      instagram: z.string(),
      github: z.string(),
      gitlab: z.string(),
    }),
  })
  .superRefine((data, ctx) => {
    // This is a custom validation function that will be called
    // before the form is submitted
    if (!data.info.experience) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.OPTION_REQUIRED,
        path: ['info.experience'],
      })
    }
    if (!data.info.firstJob) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.OPTION_REQUIRED,
        path: ['info.firstJob'],
      })
    }
    if (!data.info.flexible) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.OPTION_REQUIRED,
        path: ['info.flexible'],
      })
    }
    if (!data.info.remote) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.OPTION_REQUIRED,
        path: ['info.remote'],
      })
    }
    if (data.avatar && data.avatar.size > ONE_MB) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.AVATAR_TOO_BIG,
        path: ['avatar'],
      })
    }
  })

// Zod has a great infer method that will
// infer the shape of the schema into a TypeScript type
type FormInput = z.infer<typeof zodSchema>

const { data, pending, error, refresh } = await useFetch('/api/profile')

// Helper function to find social link URL by name
const getSocialUrl = (socials: any[], name: string): string => {
  const social = socials?.find(s => s.name.toLowerCase() === name.toLowerCase())
  return social?.url || ''
}

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  avatar: null,
  profile: {
    firstName: data.value?.personalInfo?.firstName || '',
    lastName: data.value?.personalInfo?.lastName || '',
    role: data.value?.personalInfo?.role || '',
    location: data.value?.personalInfo?.location || '',
    bio: data.value?.personalInfo?.shortBio || '',
  },
  info: {
    experience: null,
    firstJob: null,
    flexible: null,
    remote: null,
  },
  social: {
    facebook: getSocialUrl(data.value?.personalInfo?.socials, 'facebook'),
    twitter: getSocialUrl(data.value?.personalInfo?.socials, 'twitter'),
    dribbble: getSocialUrl(data.value?.personalInfo?.socials, 'dribbble'),
    instagram: getSocialUrl(data.value?.personalInfo?.socials, 'instagram'),
    github: getSocialUrl(data.value?.personalInfo?.socials, 'github'),
    gitlab: getSocialUrl(data.value?.personalInfo?.socials, 'gitlab'),
  },
} satisfies FormInput

// This is the list of options for the select inputs
const experience = ['0-2 years', '2-5 years', '5-10 years', '10+ years']
const answers = [
  {
    label: 'Yes',
    value: 'yes',
  },
  {
    label: 'No',
    value: 'no',
  },
]

// This is the computed value that will be used to display the current avatar
const currentAvatar = computed(() => data.value?.personalInfo?.picture)

const {
  handleSubmit,
  isSubmitting,
  setFieldError,
  meta,
  values,
  errors,
  resetForm,
  setFieldValue,
  setErrors,
} = useForm({
  validationSchema,
  initialValues,
})

const success = ref(false)
const fieldsWithErrors = computed(() => Object.keys(errors.value).length)

// TairoInputFileHeadless gives us a listfile input, but we need to
// extract the file from the list and set it to the form
const inputFile = ref<FileList | null>(null)
const fileError = useFieldError('avatar')
watch(inputFile, (value) => {
  const file = value?.item(0) || null
  setFieldValue('avatar', file)
})

// Ask the user for confirmation before leaving the page if the form has unsaved changes
onBeforeRouteLeave(() => {
  if (meta.value.dirty) {
    // eslint-disable-next-line no-alert
    return confirm('You have unsaved changes. Are you sure you want to leave?')
  }
})

const toaster = useNuiToasts()

// Initialize the profile composable and auth
const { updateProfile, profileLoading } = useCurrentProfile()
const { getCurrentProfileId } = useAuth()

// This is where you would send the form data to the server
const onSubmit = handleSubmit(
  async (values) => {
    success.value = false

    try {
      // Transform form values to profile update format
      const socialLinks: SocialLink[] = []

      // Add social links if they have values
      if (values.social.facebook) {
        socialLinks.push({ name: 'facebook', url: values.social.facebook, icon: 'fa6-brands:facebook-f' })
      }
      if (values.social.twitter) {
        socialLinks.push({ name: 'twitter', url: values.social.twitter, icon: 'fa6-brands:x-twitter' })
      }
      if (values.social.dribbble) {
        socialLinks.push({ name: 'dribbble', url: values.social.dribbble, icon: 'fa6-brands:dribbble' })
      }
      if (values.social.instagram) {
        socialLinks.push({ name: 'instagram', url: values.social.instagram, icon: 'fa6-brands:instagram' })
      }
      if (values.social.github) {
        socialLinks.push({ name: 'github', url: values.social.github, icon: 'fa6-brands:github' })
      }
      if (values.social.gitlab) {
        socialLinks.push({ name: 'gitlab', url: values.social.gitlab, icon: 'fa6-brands:gitlab' })
      }

      // Prepare profile update data
      const profileUpdateData = {
        firstName: values.profile.firstName,
        lastName: values.profile.lastName,
        title: values.profile.role,
        location: values.profile.location,
        shortBio: values.profile.bio,
        socialLinks,
        // Add other fields as needed
        updatedAt: new Date().toISOString(),
      }

      // Update the profile using the profile composable
      await updateProfile(profileUpdateData)

      // Refresh the data from the server
      await refresh()

      // Reset form to new values
      resetForm()

      document.documentElement.scrollTo({
        top: 0,
        behavior: 'smooth',
      })

      success.value = true
      setTimeout(() => {
        success.value = false
      }, 3000)
    }
    catch (error: any) {
      console.error('Profile update error:', error)

      // Handle specific validation errors
      if (error.message?.includes('firstName')) {
        setFieldError('profile.firstName', 'Invalid first name')
      }

      document.documentElement.scrollTo({
        top: 0,
        behavior: 'smooth',
      })

      // Error toast is handled by the useDataApi composable
      return
    }
  },
  (_error) => {
    // this callback is optional and called only if the form has errors
    success.value = false

    // you can use it to scroll to the first error
    document.documentElement.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  },
)
</script>

<template>
  <form
    method="POST"
    action=""
    class="w-full pb-16 max-w-3xl dark:[--color-input-default-bg:var(--color-muted-950)]"
    novalidate
    @submit.prevent="onSubmit"
  >
    <div class="flex items-center justify-end border-b border-muted-300 dark:border-muted-800 pb-4 mb-6">
      <div class="flex items-center gap-2">
        <BaseButton class="w-24" to="/layouts/profile">
          Cancel
        </BaseButton>
        <BaseButton
          type="submit"
          variant="primary"
          class="w-24"
          :disabled="isSubmitting || profileLoading"
          :loading="isSubmitting || profileLoading"
        >
          Save
        </BaseButton>
      </div>
    </div>
    <div>
      <div class="space-y-12">
        <BaseMessage v-if="success" @close="success = false">
          Your profile has been updated!
        </BaseMessage>
        <BaseMessage
          v-if="fieldsWithErrors"
          type="danger"
          @close="() => setErrors({})"
        >
          This form has {{ fieldsWithErrors }} errors, please check them
          before submitting
        </BaseMessage>

        <TairoFormGroup
          label="Profile picture"
          sublabel="This is how others will recognize you"
        >
          <div
            class="relative flex flex-col gap-4"
          >
            <TairoFullscreenDropfile
              icon="ph:image-duotone"
              :filter-file-dropped="(file) => file.type.startsWith('image')"
              @drop="
                (value) => {
                  inputFile = value
                }
              "
            />
            <TairoInputFileHeadless
              v-slot="{ open, remove, preview, files }"
              v-model="inputFile"
              accept="image/*"
            >
              <div class="relative size-16">
                <img
                  v-if="files?.length && files.item(0)"
                  :src="preview(files.item(0)!).value"
                  alt="Upload preview"
                  class="bg-muted-200 dark:bg-muted-700/60 size-16 rounded-full object-cover object-center"
                >
                <img
                  v-else
                  :src="currentAvatar"
                  alt="Upload preview"
                  class="bg-muted-200 dark:bg-muted-700/60 size-16 rounded-full object-cover object-center"
                >
                <div
                  v-if="files?.length && files.item(0)"
                  class="absolute -bottom-0.5 -end-0.5 z-20"
                >
                  <BaseTooltip content="Remove image">
                    <BaseButton
                      size="icon-sm"
                      rounded="full"
                      @click="remove(files.item(0)!)"
                    >
                      <Icon name="lucide:x" class="size-4" />
                    </BaseButton>
                  </BaseTooltip>
                </div>
                <div v-else class="absolute -bottom-0.5 -end-0.5 z-20">
                  <BaseTooltip content="Upload image">
                    <BaseButton
                      size="icon-sm"
                      rounded="full"
                      @click="open"
                    >
                      <Icon name="lucide:plus" class="size-4" />
                    </BaseButton>
                  </BaseTooltip>
                </div>
              </div>
            </TairoInputFileHeadless>
            <div
              v-if="fileError"
              class="text-destructive-600 inline-block font-sans text-[.8rem]"
            >
              {{ fileError }}
            </div>
          </div>
        </TairoFormGroup>

        <TairoFormGroup
          label="Profile Info"
          sublabel="Others diserve to know you more"
        >
          <div class="grid grid-cols-12 gap-4">
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="profile.firstName"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
                required
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="solar:user-rounded-linear"
                  placeholder="First name"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="profile.lastName"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
                required
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="solar:user-rounded-linear"
                  placeholder="Last name"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="profile.role"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12"
                required
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="solar:suitcase-lines-linear"
                  placeholder="Job title"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="profile.location"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12"
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="solar:map-point-linear"
                  placeholder="Location"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="profile.bio"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12"
              >
                <BaseTextarea
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  rows="4"
                  placeholder="About you / Short bio..."
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
          </div>
        </TairoFormGroup>

        <TairoFormGroup
          label="Professional Info"
          sublabel="This can help you to win some opportunities"
        >
          <div class="grid grid-cols-12 gap-4">
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="info.experience"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
                required
              >
                <BaseSelect
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  placeholder="Experience"
                  rounded="sm"
                  :items="experience.map((value) => ({
                    value,
                    textValue: value,
                  }))"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="info.firstJob"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
                required
              >
                <BaseSelect
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  placeholder="Is this your first job?"
                  rounded="sm"
                  :items="answers.map((item) => ({
                    value: item.value,
                    textValue: item.label,
                  }))"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="info.flexible"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
                required
              >
                <BaseSelect
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  placeholder="Are you flexible?"
                  rounded="sm"
                  :items="answers.map((item) => ({
                    value: item.value,
                    textValue: item.label,
                  }))"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="info.remote"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
                required
              >
                <BaseSelect
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  placeholder="Do you work remotely?"
                  rounded="sm"
                  :items="answers.map((item) => ({
                    value: item.value,
                    textValue: item.label,
                  }))"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
          </div>
        </TairoFormGroup>

        <TairoFormGroup
          label="Social Profiles"
          sublabel="This can help others finding you on social media"
        >
          <div class="grid grid-cols-12 gap-4">
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="social.facebook"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="fa6-brands:facebook-f"
                  placeholder="Facebook URL"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="social.twitter"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="fa6-brands:x-twitter"
                  placeholder="Twitter URL"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="social.dribbble"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="fa6-brands:dribbble"
                  placeholder="Dribbble URL"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="social.instagram"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="fa6-brands:instagram"
                  placeholder="Instagram URL"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="social.github"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  type="text"
                  icon="fa6-brands:github"
                  placeholder="Github URL"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange, handleBlur }"
              name="social.gitlab"
            >
              <BaseField
                v-slot="{ inputAttrs, inputRef }"
                :error="errorMessage"
                :disabled="isSubmitting"
                class="col-span-12 sm:col-span-6"
              >
                <TairoInput
                  :ref="inputRef"
                  v-bind="inputAttrs"
                  :model-value="field.value"
                  :aria-invalid="errorMessage ? 'true' : undefined"
                  icon="fa6-brands:gitlab"
                  placeholder="Gitlab URL"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </BaseField>
            </Field>
          </div>
        </TairoFormGroup>
      </div>
    </div>
    <TairoFormSave
      rounded="md"
      :disabled="isSubmitting"
      :loading="isSubmitting"
      @reset="resetForm"
    />
  </form>
</template>
