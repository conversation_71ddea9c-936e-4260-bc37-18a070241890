# App Template

This is a template for creating new applications in the PIB project.

## Creating a New App

1. Copy this template to create a new app:
```bash
cp -r apps/app-template apps/your-app-name
```

2. Update `package.json`:
   - Change `name` from `app-template` to `your-app-name`

3. Update `nuxt.config.ts`:
   - Add the layers/modules you want to include
   - Update any app-specific configuration

4. Install dependencies:
```bash
cd apps/your-app-name
pnpm install
```

5. Run the development server:
```bash
pnpm dev
```

## Extending Modules

To include modules in your app, add them to the `extends` array in `nuxt.config.ts`:

```typescript
export default defineNuxtConfig({
  extends: [
    '../../layers/tairo', // Core UI layer
    '../../layers/auth-module', // Example: Authentication module
    '../../layers/dashboard-module', // Example: Dashboard features
    // Add more modules as needed
  ]
})
```

## App Structure

```
your-app-name/
├── assets/         # App-specific assets
├── components/     # App-specific components
├── composables/    # App-specific composables
├── pages/          # App pages/routes
├── plugins/        # App plugins
├── public/         # Static files
├── server/         # Server routes
├── nuxt.config.ts  # App configuration
├── package.json    # App dependencies
└── README.md       # App documentation
```

## Best Practices

- Keep app-specific code minimal
- Most functionality should be in reusable modules
- Use the app layer for:
  - Route definitions (pages)
  - App-specific configuration
  - Composition of modules
  - Environment-specific settings
