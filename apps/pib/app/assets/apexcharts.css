@reference '~/assets/main.css';

.apexcharts-text,
.apexcharts-datalabels text,
.apexcharts-datalabel-value,
.apexcharts-xaxis-label,
.apexcharts-yaxis-label,
.apexcharts-legend-text {
  @apply font-sans font-normal text-muted-500! dark:text-muted-400! fill-current;
}

.apexcharts-bar-series .apexcharts-datalabels,
.apexcharts-rangebar-series .apexcharts-datalabels {
  @apply font-thin!;
}

.apexcharts-bar-series .apexcharts-datalabels text,
.apexcharts-rangebar-series .apexcharts-datalabels text {
  @apply text-white! font-normal! fill-current;
}

.apexcharts-gridline,
.apexcharts-gridline + line {
  @apply text-muted-200! dark:text-muted-800! stroke-current!;
}

.apexcharts-gridlines-vertical line {
  @apply text-transparent! dark:text-transparent! stroke-current!;
}

.apexcharts-subtitle-text {
  @apply text-muted-800 dark:text-muted-100! fill-current!;
}

.apexcharts-xaxis line,
.apexcharts-yaxis line,
.apexcharts-grid-borders line {
  @apply text-muted-300 dark:text-muted-700 stroke-current;
}

.apexcharts-xaxis-tick,
.apexcharts-yaxis-tick {
  @apply text-muted-300 dark:text-muted-600 stroke-current;
}

.apexcharts-series-markers .apexcharts-marker,
.apexcharts-series-bubble .apexcharts-marker {
  @apply text-white dark:text-muted-900 stroke-current;
}

.apexcharts-bar-area {
  @apply text-white dark:text-muted-900 stroke-current;
}

.apexcharts-pie-area,
.apexcharts-pie-slice-0 {
  @apply text-white dark:text-muted-900 stroke-current;
}

.apexcharts-slices .apexcharts-pie-label {
  @apply text-muted-50! dark:text-muted-100! !stroke-none fill-current!;
}

.apexcharts-radialbar-track .apexcharts-radialbar-area,
.apexcharts-track .apexcharts-radialbar-area {
  @apply text-muted-200! stroke-current;
}

.dark .apexcharts-radialbar-track .apexcharts-radialbar-area,
.dark .apexcharts-track .apexcharts-radialbar-area {
  @apply text-muted-900! stroke-current;
}

.apexcharts-radar-series > polygon,
.apexcharts-plot-series > polygon,
.apexcharts-radar-series > line,
.apexcharts-plot-series > line {
  @apply fill-transparent text-muted-200 dark:text-muted-700 stroke-current;
}

.apexcharts-tooltip {
  @apply shadow-xl! shadow-muted-400/10! dark:shadow-none!;
}

.apexcharts-xaxistooltip {
  @apply border border-muted-300! dark:border-muted-700!;
}

.apexcharts-xaxistooltip-top:before {
  @apply border-t-muted-300! dark:border-t-muted-700!;
}
.dark .apexcharts-xaxistooltip-top:before {
  @apply border-t-muted-700!;
}

.apexcharts-tooltip.apexcharts-theme-light {
  @apply border border-muted-300 dark:border-muted-700 bg-white dark:bg-muted-800 text-muted-700 dark:text-muted-100;
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  @apply border-b border-muted-200 dark:border-muted-700 bg-white dark:bg-muted-800 text-muted-700 dark:text-muted-100;
}

.dark .apexcharts-xaxistooltip,
.dark .apexcharts-yaxistooltip,
.dark .apexcharts-tooltip {
  @apply border! border-muted-700! bg-muted-800! text-muted-100!;
}

.dark .apexcharts-tooltip .apexcharts-tooltip-title {
  @apply border! border-muted-800! bg-muted-900/50! text-muted-100!;
}

.dark .apexcharts-xaxistooltip.apexcharts-xaxistooltip-bottom::before {
  @apply border-b-muted-700!;
}

.dark .apexcharts-xaxistooltip.apexcharts-xaxistooltip-bottom::after {
  @apply border-b-muted-800!;
}

.dark .apexcharts-xaxistooltip.apexcharts-xaxistooltip-top::before {
  @apply border-t-muted-700!;
}

.dark .apexcharts-xaxistooltip.apexcharts-xaxistooltip-top::after {
  @apply border-t-muted-800!;
}

.dark .apexcharts-yaxistooltip.apexcharts-yaxistooltip-left::before {
  @apply border-s-muted-700!;
}

.dark .apexcharts-yaxistooltip.apexcharts-yaxistooltip-left::after {
  @apply border-s-muted-800!;
}

.dark .apexcharts-yaxistooltip.apexcharts-yaxistooltip-right::before {
  @apply border-e-muted-700!;
}

.dark .apexcharts-yaxistooltip.apexcharts-yaxistooltip-right::after {
  @apply border-e-muted-800!;
}
