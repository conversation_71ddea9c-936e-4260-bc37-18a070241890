---
title: Troubleshooting
---

## Understanding the project stack

Nuxt is a framework that combines a set of tools and libraries to create applications. It's important to know what tools are used in order to understand how to solve potential issues.

It uses Vite as a development tool, and Roll<PERSON> to build the production version of your app.

We highly encourage you to both read their troubleshooting guides, as they might be able to help you to solve your issue.

- [Vite's troubleshooting guide](https://vite.dev/guide/troubleshooting.html): contains common issues with the development server (ex: requests stalled, hot reloading, etc.)
- [Rollup's troubleshooting guide](https://rollupjs.org/troubleshooting/): contains common issues with the production build (ex: memory issues, tree-shaking, etc.)

---

Useful resources:

- [Read troubleshooting guide on vite.dev](https://vite.dev/guide/troubleshooting.html)
- [Read troubleshooting guide on rollupjs.org](https://rollupjs.org/troubleshooting/)

## Enable CLI debug

If you encounter issues that you can't solve, you can start the project in debug mode. This will enable the verbose mode of Nuxt, which will output more information in the console.

:::code-group
```bash [Terminal]
# Enable Nuxt debug mode
DEBUG=1 pnpm dev # or pnpm build
```
:::

Vite also has a debug mode, which can be enabled by setting the `DEBUG` environment variable to `vite:*`.

::code-group
```bash [Terminal]
# Enable Nuxt & Vite debug mode
DEBUG=vite:* pnpm dev # or pnpm build
```
::

You can send those logs to us on our [support portal](https://cssninja.io/faq/support) if you need help!

## Increase the memory limit

When the documentation layer is enabled, or when building the full demo, you may need to increase the memory limit of Node.js. This is because the documentation layer uses [nuxt-component-meta](https://github.com/nuxtlabs/nuxt-component-meta) to generate the components documentation. When building the demo, Rollup will also [use a lot of memory](https://rollupjs.org/troubleshooting/#error-javascript-heap-out-of-memory) to bundle the project.

::code-group
```bash [Terminal]
# Increase the memory limit to 8GB
NODE_OPTIONS=--max-old-space-size=8192 pnpm demo:build
```
::

## Use step-by-step debugger

You can use the [VS Code debugger](https://code.visualstudio.com/docs/editor/debugging#_launch-configurations) to debug your application. This is useful when you want to inspect the state of your application at a specific time.

Tairo has a pre-configured launch configuration for VS Code. You can use it by clicking on the "Run and Debug" button in the sidebar, and then selecting "fullstack: nuxt" in the dropdown.

Then you can define breakpoints in your code, and start the debugger. The debugger will stop at the breakpoints you defined, and you will be able to inspect the state of your application.

In addition to the breakpoints, you can also use the `debugger` keyword in your code to stop the execution of your application. This is useful when you want to inspect the state of your application at a specific time.

---

Useful resources:

- [Read Debugging Guide on nuxt.com](https://nuxt.com/docs/guide/going-further/debugging#debugging-in-your-ide)
- [Read Reactivity Debugging on vuejs.org](https://vuejs.org/guide/extras/reactivity-in-depth.html#reactivity-debugging)

## Use Nuxt devtools

You can install the [Nuxt devtools](https://devtools.nuxt.com/) which provide a set of tools to inspect your application. This is useful when you want to inspect the state of your application at a specific time, inspect components, pages, etc.

First you need to install the `@nuxt/devtools` package in your project:

::code-group
```bash [Terminal]
pnpm --filter=app install --dev @nuxt/devtools
```
::

Then simply enable the devtools in your `<app>/nuxt.config.ts` file:

::code-group
```ts [<app>/nuxt.config.ts]
export default defineNuxtConfig({
  devtools: { enabled: true },
})
```
::

---

Useful resources:

- [Read Debugging Guide on nuxt.com](https://nuxt.com/docs/guide/going-further/debugging#debugging-in-your-ide)
- [Read Reactivity Debugging on vuejs.org](https://vuejs.org/guide/extras/reactivity-in-depth.html#reactivity-debugging)
