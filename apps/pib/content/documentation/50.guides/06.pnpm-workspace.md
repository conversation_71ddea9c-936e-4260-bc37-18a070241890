---
title: Pnpm workspace
---

# Introduction

Pnpm workspace is a powerful tool for managing multiple packages in a single repository. It can help you keep your dependencies in sync and reduce the time and disk space required for package installation.

If you're working on a project that uses pnpm workspace, you can take advantage of this feature to manage your packages more efficiently. Another feature of pnpm workspace is hoisting, which allows you to install dependencies at the root level instead of in each workspace. This can further reduce the disk space and installation time required for your packages.

Overall, pnpm workspace can be a powerful tool for managing multiple packages in a single repository. It can help you keep your dependencies in sync and reduce the time and disk space required for package installation. If you're new to pnpm workspace, it's worth taking the time to learn how to use it effectively.

---

Useful resources:
- [Read more about workspace on pnpm.io](https://pnpm.io/workspaces)

## Workspace structure

Pnpm use a `pnpm-workspace.yaml` file to define the workspace structure. This file is located at the root of the project and contains a list of workspace directories.

:::code-group
```yaml [pnpm-workspace.yaml]
packages:
  - .app
  - .demo
  - layers/*
```
:::

All folders listed in this file that contains a `package.json` file will be considered in the workspace. You can use wildcards to automatically include all folders that match a pattern, like with the `layers/*` entry above.

As an example, you could create a new folder for your backend and add it to the workspace:

:::code-group
```yaml [pnpm-workspace.yaml]
packages:
  - .app
  - .backend # replace unused .demo with .backend
  - layers/*
```
:::

Then create a `package.json` file in the `.backend` folder:

:::code-group
```json [.backend/package.json]
{
  "name": "my-backend",
  "private": true,
  "version": "1.0.0"
}
```
:::

## Install dependencies in the right place

By default, if you don't specify a package filter, pnpm will install dependencies in the root `package.json` file. This is not what we want when using pnpm workspace.

Instead we want to install it in the package that contains our layer. For example, if we want to install a dependency in the `.app` layer, we should use the following command:

:::code-group
```bash [Terminal]
pnpm --filter=app install --dev my-dependency
```
:::

This will find packages in the workspace that has the name `app` in their `package.json` and install the dependency in it (here it matches `<app>/package.json` name).

:::doc-info{title="Important" icon="solar:shield-warning-bold-duotone"}
We could use `my-backend` from the previous example instead of `app` to install the dependency in the `.backend` package.
:::

Another option is to run `pnpm install` in the layer directory. This will install dependencies in the layer directory instead of the root directory.

:::code-group
```bash [Terminal]
cd .app
pnpm install --dev my-dependency
```
:::

## Upgrading dependencies in a workspace

If you want to update a dependency in a workspace, you should use the `update` command with the `--recursive` flag in order to update the dependency in all layers.

:::code-group
```bash [Terminal]
pnpm update --recursive --latest --interactive
```
:::

Select dependencies you want to update and press enter.

Once the update is done, you need to remove all `node_modules` folder using `pnpm clean:all`, delete the `pnpm-lock.yaml` file and run `pnpm install` again.

:::doc-info{title="Important" icon="solar:shield-warning-bold-duotone"}
Note that it may be possible that we have patched some dependencies, updating them may or may not include our fixes. If you encounter any issue, please report it on our [Support Portal](https://cssninja/faq/support).
:::

Alternatively, you can also use `find` unix command to find all `node_modules` directory in the workspace and delete them:

:::code-group
```bash [Terminal]
find . -maxdepth 4 -name node_modules -type d -prune -exec rm -rf {} \;
```
:::

---

Useful resources:
- [Read update guide on pnpm.io](https://pnpm.io/cli/update)
