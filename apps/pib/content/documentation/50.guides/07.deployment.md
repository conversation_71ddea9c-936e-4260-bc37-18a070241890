---
title: Deployment
---

## Introduction

Before you can deploy your application, you need to know how to build it. This guide will show you how to build your application.

Nuxt have two different ways to build your application, for static hosting or server-rendered application. Each one has its own advantages and disadvantages, so it's important to know which one is best for your use case.

---

Useful resources:
- [Read more deployment on nuxt.com](https://nuxt.com/docs/getting-started/deployment)

## Static Hosting

### Advantages

- Faster load times: Static sites are prebuilt and served as-is, which means they can be delivered to the user more quickly than server-rendered sites.
- Lower hosting costs: Static sites can be hosted on services like GitHub Pages or Netlify for free or at a low cost, whereas server-rendered sites require more powerful hosting solutions.

### Disadvantages
- Limited interactivity: Static sites can't provide the same level of interactivity as server-rendered sites because they don't have a server-side component.
- Harder to update: Static sites require a rebuild and redeploy every time you make a change, which can be time-consuming and error-prone.
- Limited functionality: Static sites can't perform complex server-side operations like processing payments or sending emails without the use of third-party services.
- Limited dynamic content: Static sites can't easily display dynamic content like user-specific data or real-time updates without the use of client-side JavaScript.

### Generate a static site

Use the `pnpm generate` command to generate a static site from your application. This command will create a `<app>/.output/public` directory that contains all of the files needed to serve your application statically.

You can also directly use [Nuxt CLI](https://github.com/nuxt/cli) using `npx`, which is installed with Node.js.

:::code-group
```bash [Terminal]
npx nuxt generate .app
```
:::

:::doc-info{title="Important" icon="solar:shield-warning-bold-duotone"}
Don't forget to add the `.app` after the command to specify the layer you want to build.
:::

Before deploying, you can ensure that the generated files are correct by using the `pnpm serve` command to serve them locally.

:::code-group
```bash [Terminal]
npx serve .app/.output/public
```
:::

## Server-Rendered Applications

### Advantages

- More interactivity: Server-rendered sites can provide a higher level of interactivity because they have a server-side component that can handle user input and perform complex operations.
- Easier to update: Server-rendered sites can be updated more easily because changes can be made on the server and don't require a rebuild and redeploy.
- More functionality: Server-rendered sites can perform complex server-side operations like processing payments or sending emails without the use of third-party services.
- More dynamic content: Server-rendered sites can easily display dynamic content like user-specific data or real-time updates.

### Disadvantages
- Slower load times: Server-rendered sites require more processing on the server before they can be delivered to the user, which can result in slower load times.
- Higher hosting costs: Server-rendered sites require more powerful hosting solutions, which can be more expensive than static hosting.

### Build production application

Use the `pnpm build` command to generate a static site from your application. This command will create a `<app>/.output` directory that contains all of the files needed to serve your application with a server.

Nuxt build is performed through Nitro which can output your application for different platforms. By default, it will output a Node.js application, but it comes with many presets like for Bun, Deno, Vercel, Cloudflare, etc.

You can also directly use [Nuxt CLI](https://github.com/nuxt/cli) using `npx`, which is installed with Node.js.

:::code-group
```bash [Terminal]
NITRO_PRESET=node-cluster npx nuxt build .app
```
:::

:::doc-info{title="Important" icon="solar:shield-warning-bold-duotone"}
Don't forget to add the `.app` after the command to specify the layer you want to build.
:::

Before deploying, you can ensure that the application runs correctly by using:

:::code-group
```bash [Terminal]
node .app/.output/server/index.mjs
```
:::

### Docker example

You can also use Docker to build and run your application. This is useful if you want to deploy your application on containers like Kubernetes or Docker Swarm.

First, create a `Dockerfile` in your project root directory:

:::code-group
```dockerfile [Dockerfile]
# Build stage
FROM bitnami/node:20 AS build
WORKDIR /app

# Install pnpm with corepack
RUN corepack enable && corepack prepare pnpm@latest --activate

# Prepare pnpm dependencies using lockfile, patches and docker cache
COPY pnpm-lock.yaml .
COPY patches/ patches/
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store pnpm fetch

# Install and build the app
COPY . .
RUN pnpm install
RUN pnpm build

# Production stage
FROM bitnami/node:20 AS prod
WORKDIR /app

# Create a non-root user
RUN groupadd -g 10001 nuxt && \
  useradd -u 10001 -g nuxt nuxt \
  && chown -R nuxt:nuxt /app

# Switch to the non-root user
USER nuxt:nuxt

# Set the environment to production
ENV NODE_ENV=production

# Copy the built app from the build stage
COPY --chown=nuxt:nuxt --from=build /app/.app/.output .output

# Expose the port
EXPOSE 3000

# Start the app
CMD ["node", ".output/server/index.mjs"]
```
:::

This Dockerfile uses a multi-stage build to separate the build and production stages. The build stage installs pnpm and builds the application, while the production stage copies the built application from the build stage and runs it, using a non-root user for security reasons. This allow to keep only the built application in the production image.

Then, build and run the Docker image:

:::code-group
```bash [Terminal]
docker build --tag my-frontend-app:1.0.0 .
```
:::

Once the image is built, you can run it using:

:::code-group
```bash [Terminal]
docker run --rm --publish 3000:3000 my-frontend-app:1.0.0
```
:::

---

Useful resources:
- [View Presets on nitro.unjs.io](https://nitro.unjs.io/deploy/)
- [Read about multi-stage build on docker.com](https://docs.docker.com/build/building/multi-stage/)
