---
title: Migration from v1
---

# Migration from v1

---

**TL;DR**

- Tairo is now one layer which includes all layouts.
- `app.config.ts` configuration is no longer supported, instead you have to create your own layout, based on new tairo components.
- Panels has been reworked and no longer relies on `app.config.ts` composable.
- Tailwind 4 is used which includes major changes about the way to customize the design of your application.
- Moved from Headless UI to Reka UI as barebone components library.
- `Base` components require a migration, see [Shuriken UI v4 Migration Guide](https://shurikenui.com/docs/advanced/migration) for more information.

---

## Tairo is now one layer

### New layout system

In Tairo V1, we had 4 layers:
- `tairo` (core components)
- `tairo-sidebar` (layout sidebar)
- `tairo-collapse` (layout collapse)
- `tairo-topnav` (layout topnav)

Each nuxt layout layers was registering a layout in nuxt and relying on `app.config.ts` to configure it.

In Tairo V2, we have only one layer `tairo` which does not register any layout in nuxt, and does not rely on `app.config.ts` to configure it.

Instead, we have new components to create your own layouts, allowing you to define your own logic (like RBAC, translations, async data, etc).

All layouts are using CSS variables to customize them, so you will be able to customize them as you want without needing to override any components!

You can take a look for each new structure here:
- :doc-linker{to="TairoCollapseLayout"}
- :doc-linker{to="TairoSidebarLayout"}
- :doc-linker{to="TairoSidenavLayout"} _(new layout!)_
- :doc-linker{to="TairoTopnavLayout"}

We've provided you default implementations examples in the `.demo/app/layouts` directory. But possibilities are endless, you will be able to create the layout that best fits your project requirements.

Here is a quick example of how to create a new layout:

::code-group
```vue [<app>/app/layouts/default.vue]
<template>
  <TairoCollapseLayout>
    <TairoCollapseSidebar>
      <TairoCollapseSidebarHeader>My App</TairoCollapseSidebarHeader>
      <TairoCollapseSidebarLinks class="px-4 space-y-1 grow">
        <TairoCollapseSidebarLink
          to="/"
          label="Home"
          icon="solar:home-smile-angle-outline"
        />
      </TairoCollapseSidebarLinks>
    </TairoCollapseSidebar>

    <TairoCollapseContent>
      <slot />
    </TairoCollapseContent>
  </TairoCollapseLayout>
</template>
```
::

### Panels rework

In previous version, panels were using `app.config.ts` to configure them. Now, they are using a new composable `usePanels` to create and show panels.

**Before:**

::code-group
```vue [<app>/components/MyComponent.vue]
<script setup lang="ts">
const { open } = usePanels()

function onSomeEvent() {
  // open a panel by name, which is registered in app.config.ts
  open('panel-name', {
    // ... panels props
  })
}
</script>
```
```ts [<app>/app.config.ts]
export default defineAppConfig({
  tairo: {
    panels: [
      {
        // Unique name of the panel, used to open it
        name: 'panel-name',
        component: {
          // The component name of the panel and default props
          name: 'PanelComponent',
          props: {
            color: 'primary',
          }
        },
        // The position of the panel
        position: 'left',
        // Whether to show an overlay when the panel is open
        overlay: false,
      },
    ],
  },
})
```
```vue [<app>/components/global/PanelComponent.vue]
<script setup lang="ts">
// You can add props to the component
const props = withDefaults(
  defineProps<{
    color?: 'default' | 'primary' | 'secondary'
    projects?: any[]
  }>(),
  {
    color: 'default',
    projects: () => [],
  },
)

// Eiter define events
const emits = defineEmits<{
  save: [info: any]
}>()

// And use panels utils
const { close } = usePanels()
</script>

<template>
  <BaseButtonGroup>
    <BaseButton @click="() => emits('save', { selected: props.projects[0] })">
      Save
    </BaseButton>
    <BaseButton @click="() => close()">
      Cancel
    </BaseButton>
  </BaseButtonGroup>
</template>
```
::

**After:**

::code-group
```vue [<app>/app/layouts/default.vue]
<script setup lang="ts">
import { MyPanelComponent } from '#components'

const { open } = usePanels()

async function onSomeEvent() {
  // open a panel using a component
  const emitted = await open(MyPanelComponent, {
    // ... typed panels props
  })

  // emitted contains the properties
  // passed to the `close` event from the panel component
  console.log('panel closed', emitted)
}
</script>
```
::

## Shuriken UI v4

Shuriken UI v4, which contains all `Base*` components, is a major update that is based on Tailwind 4 and Reka UI

We decided to move from Headless UI to [Reka UI](https://reka-ui.com/) as barebone components library.

This allows us to remove dependencies like `@vueform/slider`, `@vueform/multiselect`, `@cssninja/nuxt-toaster` and `v-tooltip` as we are now using Shuriken UI components.

Inputs components now render only one element rather than multiple element (for the input, label, icon, etc). In order to achieve same result, a new component `BaseField` was created.

:::doc-info{title="Migration guide" icon="solar:shield-warning-bold-duotone"}
We recommend you to read the [Shuriken UI v4 Migration Guide](https://shurikenui.com/docs/advanced/migration) to understand the changes as it contains major breaking changes that you will need to handle.
:::

## Tailwind 4

In Previous version, we were using Tailwind 3 and we were using `withShurikenUI` to extend the tailwind config.

With Tailwind 4, we are using CSS variables to customize the design (like colors, fonts, etc) of your application, and we are not using `withShurikenUI` anymore.

We recommends you to read [Tailwind Changes from v3](https://tailwindcss.com/docs/upgrade-guide#changes-from-v3){target="_blank"} guide to understand the changes as it contains some small breaking changes that are good to know.

**Before:**

::code-group
```ts [<app>/tailwind.config.ts]
import { withShurikenUI } from '@shuriken-ui/tailwind'
import colors from 'tailwindcss/colors'

export default withShurikenUI({
  content: [],
  theme: {
    extend: {
      // change the primary color
      primary: colors.violet,
    }
  }
})
```
::

**After:**

::code-group
```css [<app>/app/assets/main.css]
@import 'tailwindcss';
@import '@shuriken-ui/nuxt';
@import '#layers/@cssninja/tairo/theme.css';

@theme {
  --color-primary-50: var(--color-sky-50);
  --color-primary-100: var(--color-sky-100);
  --color-primary-200: var(--color-sky-200);
  --color-primary-300: var(--color-sky-300);
  --color-primary-400: var(--color-sky-400);
  --color-primary-500: var(--color-sky-500);
  --color-primary-600: var(--color-sky-600);
  --color-primary-700: var(--color-sky-700);
  --color-primary-800: var(--color-sky-800);
  --color-primary-900: var(--color-sky-900);
  --color-primary-950: var(--color-sky-950);
}
```
::

---

Useful resources:

- [Tailwind 4 Upgrade Guide](https://tailwindcss.com/docs/upgrade-guide#changes-from-v3)
- [Shuriken UI v4 Migration Guide](https://shurikenui.com/docs/advanced/migration)
- [Reka UI Introduction](https://reka-ui.com/docs/overview/introduction)

---
