---
title: Panels
description: Tairo panels allow to display additional data and details in animated left or right drawers.
components:
  - TairoPanels
icon:
  src: /img/illustrations/components/logo-icon.svg
  srcDark: /img/illustrations/components/logo-icon.svg
---

# Tairo Panels

Tairo panels allow to display additional data and details in animated left or right drawers.

## Usage

### Create a panel component

To create a panel component, you only need to register `close` event. You can also define props to pass data to the panel or add custom data to close event to get back when opening the panel.

::code-group
```vue [<app>/app/components/PanelComponent.vue]
<script setup lang="ts">
interface Project {
  name: string
}

// Props are optional, you will be able to pass them when opening the panel
const props = withDefaults(
  defineProps<{
    color?: 'default' | 'primary' | 'secondary'
    projects?: Project[]
  }>(),
  {
    color: 'default',
    projects: () => [],
  },
)

// Define close event, you will be able to get back the selected data from the panel
const emits = defineEmits<{
  close: [selected?: Project]
}>()
</script>

<template>
  <div>
    <BaseButton
      @click="() => emits('close', {
        selected: props.projects[0],
      })"
    >
      Select project
    </BaseButton>
    <BaseButton @click="() => emits('close')">
      Cancel
    </BaseButton>
  </div>
</template>
```
::

### Open a panel
Once you have created your panel component, you can open it using the `open` function from the `usePanels` composable.

::code-group
```vue [<app>/app/components/MyComponent.vue]
<script setup lang="ts">
import { PanelComponent } from '#components'

const { open, close } = usePanels()

function onSomeEvent() {
  // open a panel and wait for close event
  const [selected] = await open(
    // component to open
    PanelComponent,
    // panel component props
    {
      color: 'secondary',
      projects: [
        { name: 'Project 1' },
        { name: 'Project 2' },
        { name: 'Project 3' },
        { name: 'Project 4' },
      ],
    },
    // panel options
    {
      position: 'left',
      size: 'md',
      overlay: true,
    }
  )

  console.log(selected)
}
</script>
```
::

## Example

### Language panel

<!-- demo: #examples/panel/language -->

### Activity panel

<!-- demo: #examples/panel/activity -->

### Search panel

<!-- demo: #examples/panel/search -->

### Task panel

<!-- demo: #examples/panel/task -->
