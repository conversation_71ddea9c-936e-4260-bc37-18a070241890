---
title: Form validation
description: Form validation is a way to check if the data entered by the user is valid. <PERSON><PERSON> provides a simple way to validate forms using the `veevalidate` and `zod` libraries.
---

## Form validation

Form validation is a way to check if the data entered by the user is valid. <PERSON><PERSON> provides a simple way to validate forms using the `vee-validate` and `zod` libraries.

Vee-validate provides vue components and composable to control fields and form whereas zod is a library to define schemas. In the example below, we show how both work with <PERSON><PERSON> with a simple user profile form.

<!-- demo: #examples/tairo/validation -->

:::doc-info{title="Optional dependencies" icon="solar:shield-warning-bold-duotone"}
Note that you can totally remove those dependencies, <PERSON><PERSON> and Shuriken UI components won't be affected.
:::
