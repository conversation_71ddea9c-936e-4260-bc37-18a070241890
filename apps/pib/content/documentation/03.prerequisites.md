---
title: Before you start
---

# Before you start

## Get your access

Tairo is a premium template sold on [ThemeForest](https://go.cssninja.io/buy-tairo). You have to purchase a license to get access to the source.

Once you own a license, you will be able to download the source from your ThemeForest account. You can also get your [github lifetime access](https://cssninja.io/faq/github-access) from us, which will allow you to get the latest updates, before they're even released on ThemeForest.

:::doc-info{title="Important" icon="solar:shield-warning-bold-duotone"}
Remember to make and keep your repository **private** if you fork or create a new git repository.
:::

---

Useful resources:

- [Read our FAQ on cssninja.io](https://cssninja.io/faq/)
- [Licences broken down on cssninja.io](https://cssninja.io/faq/licenses)

---

## Get help from our support team

When purchasing a license, you get access to our [integrated support portal](https://cssninja.io/faq/support) up to six months after your order. You can ask questions, send your feedback, report bugs and get help from our support team.

Please read the [troubleshooting guide](/documentation/guides/troubleshooting) before asking for help, as it may help you solve your issue faster.

Once your support period is over, we still give you the opportunity to ask questions on our support portal, but we cannot guarantee a response.

In any case, we encourage you to join us on our [discord server](https://go.cssninja/discord), where you can find previous answers to common questions and get help from the community. Feel free to share your feedback, ideas and promote your projects!

:::doc-info{title="Community" icon="logos:discord-icon"}
We built a nice and helping discord community. Join our [Discord server](https://go.cssninja/discord) to get help from the community, anywhere anytime.
:::

---

Useful resources:

- [Read our FAQ on cssninja.io](https://cssninja.io/faq/)
- [Licences broken down on cssninja.io](https://cssninja.io/faq/licenses)

---

## Prerequisites

You need to have [Node.js LTS](https://nodejs.org/en/) installed on your machine.

*We do not recommend using the latest version of Node.js as they might introduce breaking changes and produce unexpected results with underlying dependencies.*

::code-timeline{.mt-12}
  :::code-timeline-item{vertical}
  ::code-group
    ```bash [Terminal]
    node -v
    ```
  ::

  #title
  Verify Node.js installation

  #description
  You should see the version of Node.js you just installed, it should be at least 22.x.x
  :::

  :::code-timeline-item{vertical}
  ::code-group
    ```bash [Terminal]
    corepack enable
    ```
  ::

  #title
  Enable corepack

  #description
  [Corepack](https://nodejs.org/docs/latest-v22.x/api/corepack.html) is installed by default with Node.js. It allows you to install packages manager, like yarn or pnpm.
  :::

::

:::doc-info{title="Use WSL" icon="solar:shield-warning-bold-duotone"}
[WSL](https://learn.microsoft.com/en-us/windows/wsl/install) is a compatibility layer for running Linux binary executables natively on Windows. It allows you to run a full-fledged Linux environment on Windows, which is very useful for web development.
:::

---

Useful resources:

- [Install nvm tool from github.com](https://github.com/nvm-sh/nvm)
- [Download Node.js on nodejs.org](https://nodejs.org/en)

---
