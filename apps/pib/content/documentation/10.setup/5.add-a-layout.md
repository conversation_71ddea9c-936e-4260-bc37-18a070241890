---
title: Build your layout
---

# Build your layout

Layouts define the navigation and structure of your application.

## Overview

A layout is a wrapper component that surrounds your page content. It can be used to define a common structure for your pages.

To create a layout, you need to create a new file in the `<app>/app/layouts/` directory.

The name of the file should be the name of the layout, with special case for the default layout that should be named `default.vue`.

Tairo provide a set of components that can be used to create your own layout that fits your project requirements.

### Choose a layout to start with

- TairoSidenav
- TairoSidebar
- TairoCollapse
- TairoTopnav

## Sidenav layout example

Let's create a new layout using the `TairoSidenav*` components. This allow you to create a sidebar navigation experience by combining the all the components together.

You will be able to add your own logic to the layout, depending on what you need (ex: show items for only logged users, translate items, etc...).

:::code-group
```vue [<app>/app/layouts/default.vue]
<template>
  <TairSidenavLayout>
    <!-- Navigation -->
    <TairoSidenavSidebar>
      <TairoSidenavSidebarHeader>
        <NuxtLink to="/">
          My App
        </NuxtLink>
      </TairoSidenavSidebarHeader>

      <TairoSidenavSidebarLinks class="p-4">
        <TairoSidenavSidebarLink
          to="/"
          label="Home"
          icon="solar:bolt-linear"
        />
      </TairoSidenavSidebarLinks>
    </TairoSidenavSidebar>

    <!-- Page wrapper -->
    <TairSidenavLayoutContent>
      <slot />
    </TairSidenavLayoutContent>
  </TairSidenavLayout>
</template>
```
:::

---

Useful resources:

- [Learn about layouts directory on nuxt.com](https://nuxt.com/docs/guide/directory-structure/layouts)

---
