---
title: Your first page
---

# Your first page

## Overview

The pages in a Nuxt application are located in the `<app>/app/pages/` directory. Each file in this directory corresponds to a route in your application, you can create nested directories to create nested routes.

Files can also be named with square brackets to create dynamic routes, everything is well detailed in the [routing documentation on nuxt.com](https://nuxt.com/docs/getting-started/routing).

This is a basic example of directory structure for your `<app>/app/pages/` directory:

:::code-group
```bash [Terminal]
pages/
├── app/
│   ├── posts/
│   │   ├── [id]/ # contains dynamic parameter named "id"
│   │   │   ├── edit.vue # /app/posts/1/edit
│   │   │   └── index.vue # /app/posts/1
│   │   ├── create.vue # /app/posts/create
│   │   └── index.vue # /app/posts
│   └── index.vue # /app
├── app.vue # custom layout wrapper for the app/ directory
├── login.vue # /login
└── index.vue # /
```
:::

## Let's write your first page

Now that you have your project set up, it's time to create your first page.

We can define custom meta information for the page using the `useHead` function, and define the page logic using the `<script setup>` tag.

::code-group
```vue [<app>/app/pages/index.vue]
<script setup lang="ts">
// meta information can be added to the head
useHead({
  title: 'My first page',
  meta: [
    { name: 'description', content: 'My amazing dashboard.' }
  ],
})

// here you can define your page logic
const message = ref('Hello Tairo')
</script>

<template>
  <div>
    <BaseInput v-model="message" />
  </div>
</template>
```
::

---

Useful resources:

- [Learn about pages directory on nuxt.com](https://nuxt.com/docs/guide/directory-structure/pages)

---
