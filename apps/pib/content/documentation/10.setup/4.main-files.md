---
title: Create main files
---

# Create main files
Here we will point out the main files that you need to create in your project.

## Create the app.vue

This is the first loaded component of your application. You should keep this file as simple as possible.

We recommend to use it to define the global meta information of your application. You can also register global components here like global modals, notifications, etc... but keep in mind that this file is loaded on every page.

Here we register the `BaseProvider` component, which is a wrapper for the whole application that provides global context for `@shuriken-ui/nuxt` components, and the `NuxtLayout`/`NuxtPage` components that are provided by Nuxt.

:::code-group
```vue [<app>/app/app.vue]
<script setup lang="ts">
useHead({
  htmlAttrs: {
    lang: 'en',
    dir: 'ltr',
  },
  link: [
    {
      rel: 'icon',
      type: 'image/png',
      href: '/img/favicon.png',
    },
  ],
})
</script>

<template>
  <BaseProvider>
    <NuxtRouteAnnouncer />
    <NuxtLoadingIndicator color="var(--color-primary-500)" />

    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </BaseProvider>
</template>
```
:::

---

Useful resources:

- [How Views works on nuxt.com](https://nuxt.com/docs/getting-started/views#appvue)
- [app.vue on nuxt.com](https://nuxt.com/docs/guide/directory-structure/app)

---

## Create the error.vue

This file is used in case of an error in your application, like a 404 error. This prevents the user from seeing the default Nuxt error page.

Tairo provides a `TairoError` component that you can use to display a custom error page.

:::code-group
```vue [<app>/app/app.vue]
<script setup lang="ts">
import type { NuxtError } from '#app'

const props = defineProps<{
  error: NuxtError
}>()
</script>

<template>
  <div>
    <TairoError :error="props.error" />
  </div>
</template>
```
:::

---

Useful resources:

- [Error Page on nuxt.com](https://nuxt.com/docs/getting-started/error-handling#error-page)
- [error.vue on nuxt.com](https://nuxt.com/docs/guide/directory-structure/error)

---
