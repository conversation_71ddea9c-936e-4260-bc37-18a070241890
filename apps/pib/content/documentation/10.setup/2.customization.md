---
title: Customizing Tairo
---

# Customizing Tairo
This guide will help you setup Tairo in your Nuxt project.

## Overview

Tairo use [Shuriken UI](https://shurikenui.com/) as the main UI components library which is based on [Tailwind CSS :icon{name="logos:tailwindcss-icon" .inline .mb-1 .scale-90}](https://tailwindcss.com/). This allows you to customize the design of your application by changing the Tailwind theme.

## Theme configuration

To customize the design of your application, you can use the `@theme` directive in your `main.css` file. This directive allows you to override the default Tailwind theme.

::code-group
```css [<app>/app/assets/main.css]
@import 'tailwindcss';
@import '@shuriken-ui/nuxt';
@import '#layers/@cssninja/tairo/theme.css';

@theme {
  /* Override Tailwind values */
  --font-sans: Inter, sans-serif;
  --spacing: 0.275rem;

  /* Override Tairo values */
  --tairo-sidenav-sidebar-width: 16.25rem;

  /* Override Shuriken UI values */
  --color-primary-50: var(--color-sky-50);
  --color-primary-100: var(--color-sky-100);
  --color-primary-200: var(--color-sky-200);
  --color-primary-300: var(--color-sky-300);
  --color-primary-400: var(--color-sky-400);
  --color-primary-500: var(--color-sky-500);
  --color-primary-600: var(--color-sky-600);
  --color-primary-700: var(--color-sky-700);
  --color-primary-800: var(--color-sky-800);
  --color-primary-900: var(--color-sky-900);
  --color-primary-950: var(--color-sky-950);

  --color-card-default-bg: var(--color-white);
  --color-card-default-border: var(--color-muted-300);

  /* Create your own theme variables */
  --color-custom: oklch(0.74 0.17 40.24);
}
```
::

---

Useful resources:

- [Theming on shurikenui.com](https://shurikenui.com/docs/theming)

---

## Loading fonts

Use the [`@nuxt/fonts`](https://fonts.nuxt.com/){:target="_blank"} module to load custom fonts in your Nuxt.js project. Note that the fonts you define as css variables in the `main.css` file will be automatically loaded by the `@nuxt/fonts` module.

::code-group
```bash [Terminal]
pnpm add -D @nuxt/fonts
```
::

::code-group
```ts [nuxt.config.ts]
export default defineNuxtConfig({
  modules: [
    '@nuxt/fonts', // [!code ++]
  ],
  fonts: {
    experimental: {
      processCSSVariables: true, // [!code ++]
    },
  },
})
```
::
