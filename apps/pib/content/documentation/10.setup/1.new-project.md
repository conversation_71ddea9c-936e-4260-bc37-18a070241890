---
title: Start a clean project
---

# Start a clean project
This guide will help you setup Tairo in your Nuxt project.

## Create a new Nuxt project

This command will create a new Nuxt project with minimal configuration and dependencies.

::code-group
  ```bash [pnpm]
  pnpm create nuxt <app>
  ```
  ```bash [bun]
  bun create nuxt <app>
  ```
  ```bash [yarn]
  yarn create nuxt <app>
  ```
  ```bash [npm]
  npm create nuxt <app>
  ```
::

*This will create `<app>` folder in your current directory containing an empty Nuxt project.*

You can now navigate to the newly created project folder and install dependencies:

::code-group
```bash [Terminal]
cd <app>
```
::

::code-group
  ```bash [pnpm]
  pnpm add --save-dev @shuriken-ui/nuxt tailwindcss
  ```
  ```bash [bun]
  bun add --dev @shuriken-ui/nuxt tailwindcss
  ```
  ```bash [yarn]
  yarn add --dev @shuriken-ui/nuxt tailwindcss
  ```
  ```bash [npm]
  npm add --save-dev @shuriken-ui/nuxt tailwindcss
  ```
::

### Enable Nuxt 4 compatibility

This optionnal step allows you to use the latest version of Nuxt with Tairo.

::code-group
```ts [<app>/nuxt.config.ts]
export default defineNuxtConfig({
  future: {
    compatibilityVersion: 4,
  },
})
```
::

---

Useful resources:

- [New Project on nuxt.com](https://nuxt.com/docs/getting-started/installation#new-project)
- [Testing Nuxt 4 on nuxt.com](https://nuxt.com/docs/getting-started/upgrade#testing-nuxt-4)

---

## Setup Tailwind CSS

Create the `main.css` file where you will import Tailwind CSS and Shuriken UI styles.

::code-group
```ts [<app>/nuxt.config.ts]
export default defineNuxtConfig({
  css: [
    '~/assets/main.css',
  ],
})
```
::

::code-group
```css [<app>/app/assets/main.css]
@import 'tailwindcss';
@import '@shuriken-ui/nuxt';
@import '#layers/@cssninja/tairo/theme.css';

@theme {
  /* Your custom theme */
}
```
::

---

Useful resources:

- [Theming on shurikenui.com](https://shurikenui.com/docs/theming)

---

## Load the Tairo layer

In order to make use of the Tairo components, you need to extends from Tairo layer. As tairo is a private repository, npm package is not available.

### Option 1: Embed in your project

This is the simpliest way to use Tairo, but you will not be able to update it easily.

1. Download the source code either from themeforest.net, cssninja.io or clone the repository from Github.
2. Copy the `layers/tairo` folder to your project, keeping the same structure. You don't need to add it to `nuxt.config.ts` file, as Nuxt will automatically load it.

That's it! You can now install the dependencies and run the development server.

### Option 2: Link with Github

This step requires you to have unlocked your [github access](https://cssninja.io/faq/github-access) and have a [github personal access token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens) with the `repo` scope for classic token, or read access to the Tairo repository for fine-grained access token.

*The token is only need when installing. It is also needed on CI/CD environments.*

1. Add your Github token to your environment variables.

   ::code-group
   ```bash [<app>/.env]
   GIGET_AUTH=ghp_your_github_token
   ```
   ::

2. Link your project to the Tairo layers by adding them to the `nuxt.config.ts` file.

   The version `__TAIRO_VERSION__` is the latest stable version of Tairo, you can also use the `edge` version which contains the current development version (useful for testing new features).

   ::code-group
   ```ts [<app>/nuxt.config.ts]
   export default defineNuxtConfig({
     extends: [
       'gh:cssninjaStudio/tairo/layers/tairo#__TAIRO_VERSION__',
     ],
   })
   ```
   ::

#### Troubleshooting

:icon{name="ph:warning" .inline} If you forgot to set the `GIGET_AUTH` environment variable, if your token does not allow access to Tairo repository, or if you set a non existing version, you will see an error message like this:

:::code-group
```bash [Terminal]
 ERROR  Failed to download https://api.github.com/repos/cssninjaStudio/tairo/tarball/__TAIRO_VERSION__: 404 Not Found
```
:::

---

Useful resources:

- [Github Access on cssninja.io](https://cssninja.io/faq/github-access)
- [Manage Access Tokens on docs.github.com](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens)

---

## Install dependencies

Additionnal dependencies are required to use Tairo with Nuxt, add them to your project.

::code-group
```bash [pnpm]
pnpm add --save-dev @shuriken-ui/nuxt tailwindcss
```
```bash [bun]
bun add --dev @shuriken-ui/nuxt tailwindcss
```
```bash [yarn]
yarn add --dev @shuriken-ui/nuxt tailwindcss
```
```bash [npm]
npm add --save-dev @shuriken-ui/nuxt tailwindcss
```
::

## Run the development server

Start nuxt from the current folder and open the frontend in your browser.

::code-group
```bash [pnpm]
pnpm dev
```
```bash [bun]
bun dev
```
```bash [yarn]
yarn dev
```
```bash [npm]
npm run dev
```
::

:::doc-info{title="Dev tips" icon="solar:programming-bold-duotone"}
Once you are done, you can stop the de"solar:shield-warning-bold-duotonevelopment server by pressing `Ctrl+C` (or `⌘+C` on macOS) in your terminal.
:::
