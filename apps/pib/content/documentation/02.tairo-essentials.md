---
title: Tairo essentials
---

# Tairo essentials

Discover key information about <PERSON><PERSON>, including its design system, and the technologies it uses.

:doc-stacks

## What is Tairo?

Tairo is a modern design system to build [Nuxt :icon{name="logos:nuxt-icon" .inline .mb-1 .scale-90}](https://nuxt.com/) and [Tailwind CSS :icon{name="logos:tailwindcss-icon" .inline .mb-1 .scale-90}](https://tailwindcss.com/) applications and websites, leveraging the power of [Vite :icon{name="logos:vitejs" .inline .mb-1 .scale-90}](https://vite.dev/) and [Nitro :icon{name="unjs:nitro" .inline .mb-1 .scale-90}](https://nitro.build/).

Tairo is composed of two main parts:
- **A Nuxt Layer**: which include a set of reusable components to let you build your application faster.
- **A demo project**: that shows you how to use these components.

## What is Tailwind CSS?

With Tailwind CSS, developers of all skill levels can efficiently craft modern, responsive, and unique designs, making it an invaluable tool in web development.

Tailwind provides a vast set of low-level utility classes that can be applied directly in components, eliminating the need to write custom CSS code.

:::doc-info{title="Tailwind V4" icon="devicon:tailwindcss"}
We recently added support for Tailwind CSS v4.0.0. You can now use the latest features and improvements in your projects.
:::

---

Useful resources:

- [Visit tailwindcss.com](https://tailwindcss.com/)
- [Try it on tailwind playground](https://play.tailwindcss.com/)

---

## What is Nuxt?

Nuxt is a full-stack framework that combines [Vite :icon{name="logos:vitejs" .inline .mb-1}](https://vite.dev/), [Vue.js :icon{name="logos:vue" .inline .mb-1 .scale-90}](https://vuejs.org/) and [Nitro :icon{name="unjs:nitro" .inline .mb-1}](https://nitro.build/) to create universal applications.

With a few settings, you can swap from a full SSR application to a static application, or even a combination of both. Create full server applications, or create a full stack application with a single framework, including fully typed code, optimized payload, and more.

It also has out-of-the-box support for a high number of pre configured providers, including Edge Worker, such as [Cloudflare Workers](https://nitro.unjs.io/deploy/providers/cloudflare), [Vercel](https://nitro.unjs.io/deploy/providers/vercel), [Netlify](https://nitro.unjs.io/deploy/providers/netlify), [Firebase](https://nitro.unjs.io/deploy/providers/firebase), and more.

### What is a Nuxt Layer?

Layers is a mechanism that allows you to split your application into multiple directories, each with their own config file. Each directory can be a full Nuxt application, with its own pages, components, plugins, and more.

At the end, all layers are merged together, representing a single application.

---

Useful resources:

- [Visit nuxt.com](https://nuxt.com/)
- [Read introduction guide on nuxt.com](https://nuxt.com/docs/getting-started/introduction)
- [Read pages and layouts guide on nuxt.com](https://nuxt.com/docs/migration/pages-and-layouts)
- [Learn Nuxt Layers with Alexander Lichter on youtube.com](https://www.youtube.com/watch?v=fr5yo3aVkfA)

---

## What is Vue.js?

Vue.js is a progressive JavaScript framework designed to simplify the development of interactive user interfaces and single-page applications.

Vue.js focuses on the view layer, offering flexibility to integrate seamlessly with other libraries or existing projects. Among its key benefits is a reactivity system, which efficiently updates the UI whenever data changes, delivering a smooth user experience.

It also features a component-based architecture, enhancing code reusability and maintainability.

---

Useful resources:

- [Visit vuejs.org](https://vuejs.org/)
- [Learn template syntax on vuejs.org](https://vuejs.org/guide/essentials/template-syntax.html)
- [Learn reactivity fundamentals on vuejs.org](https://vuejs.org/guide/essentials/reactivity-fundamentals.html)

---

## What is Nitro?

Nitro is a game-changing server toolkit designed to simplify and streamline the development of server-side applications.

With standout features like zero-configuration setup, intuitive file system routing, and seamless deployment to multiple platforms, it takes the pain out of server development.

Built on the efficient [h3 :icon{name="unjs:h3" .inline .mb-1}](https://h3.unjs.io/) engine, Nitro delivers high performance while integrating smoothly with the broader [UnJS ecosystem](https://unjs.io/).

---

Useful resources:

- [Visit nitro.build](https://nitro.build/)
- [Visit unjs.io](https://unjs.io/)

---

## What is Vite?

Vite is a modern build tool and development server designed to streamline the frontend development experience for web applications.

It serves code directly to the browser during development, eliminating the need for upfront bundling and resulting in near-instant startup times and hot module replacement (HMR). This allows developers to see changes reflected in real-time, boosting productivity.

For production, Vite uses [Rollup :icon{name="logos:rollupjs" .inline .mb-1}](https://rollupjs.org/) to create optimized, efficient bundles, ensuring high performance.

---

Useful resources:

- [Visit vite.dev](https://vite.dev/)
- [Read introduction guide on nuxt.com](https://nuxt.com/docs/getting-started/introduction)

---

## What is Shuriken UI?

[Shuriken UI :icon{name="nui-icon:shurikenui-icon" .inline .mb-1}](https://shurikenui.com/) is a component library that provides a set of accessible and customizable components to build functional interfaces. It is designed to be easy to use and integrate with Nuxt.

It is built on top of Tailwind CSS, a utility-first CSS framework that provides a set of utility classes to style your components, and Reka UI, an open-source library with unstyled, primitive components.

---

Useful resources:

- [Visit shurikenui.com](https://shurikenui.com)
- [Read documentation guides](https://shurikenui.com/docs)

---

## What is Reka UI?

[Reka UI :icon{name="nui-icon:rekaui-icon" .inline .mb-1}](https://reka-ui.com/docs/overview/introduction?utm_source=chatgpt.com) is an open-source UI component library designed for building high-quality, accessible design systems and web applications using Vue.js. Previously known as Radix Vue, it was rebranded as Reka UI in its v2 evolution. Reka has some very interesting key features:

- **Accessibility-First**<br />
  Components adhere to WAI-ARIA design patterns, ensuring effective interaction for all users.

- **Customizable & Unstyled**<br />
  Components are unstyled by default, allowing developers to apply their preferred styling methods, such as vanilla CSS, preprocessors, or CSS-in-JS libraries.

- **Open & Modular**<br />
  The architecture enables easy customization, allowing developers to wrap, extend, or modify each component as needed.

- **Flexible State Management**<br />
  Components support both controlled and uncontrolled modes, offering a balance between flexibility and ease of use.

---

Useful resources:

- [Visit reka-ui.com](https://reka-ui.com)
- [Read introduction guide](https://reka-ui.com/docs/overview/introduction)

---
