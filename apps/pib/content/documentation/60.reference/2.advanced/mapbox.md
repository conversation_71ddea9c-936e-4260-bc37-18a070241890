---
title: Location picker Input
navigation:
  title: mapbox-gl (location picker)
components:
  - AddonMapboxLocationPicker
description: Location picker input component with mapbox.
icon:
  src: /img/illustrations/components/apexcharts-icon.svg
  srcDark: /img/illustrations/components/apexcharts-icon.svg
---

# Location picker input

:::doc-info{title="Optional dependencies" icon="solar:shield-warning-bold-duotone"}
This component uses the [mapbox-gl](https://docs.mapbox.com/mapbox-gl-js/guides) with [mapbox-gl-geocoder](https://github.com/mapbox/mapbox-gl-geocoder) to render the location picker.
Make sure to install the dependencies and configure the mapbox access token in your project
:::

Mapbox location picker input component.

## Example

<!-- demo: #examples/addons/mapbox -->

## Components

### AddonMapboxLocationPicker

:doc-component-meta{name="AddonMapboxLocationPicker"}
