---
title: Charts with Apexcharts
navigation:
  title: apexcharts
components:
  - AddonApexcharts
description: Tairo ships with the Apexcharts data visualization library customized for Vue and Nuxt. Use the component to render charts and graphs.
icon:
  src: /img/illustrations/components/apexcharts-icon.svg
  srcDark: /img/illustrations/components/apexcharts-icon.svg
---

# Charts with Apexcharts

:::doc-info{title="Optional dependency" icon="solar:shield-warning-bold-duotone"}
This component uses the [vue3-apexcharts](https://github.com/apexcharts/vue3-apexcharts) library, which is a wrapper for the [ApexCharts](https://apexcharts.com/) library. We also provide chart styles for dark mode support.
:::

Display charts and graphs with the Apexcharts library.

## Example

<!-- demo: #examples/apexcharts/base -->

## Components

### AddonApexcharts

:doc-component-meta{name="AddonApexcharts"}
