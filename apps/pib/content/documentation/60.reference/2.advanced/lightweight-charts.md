---
title: Charts with Lightweight Charts
navigation:
  title: lightweight-charts
components:
  - AddonLightweightCharts
description: <PERSON><PERSON> ships with the Lightweight Charts data visualization library customized for Vue and Nuxt. Use the component to render charts and graphs.
icon:
  src: /img/illustrations/components/apexcharts-icon.svg
  srcDark: /img/illustrations/components/apexcharts-icon.svg
---

# Charts with Lightweight Charts

:::doc-info{title="Optional dependency" icon="solar:shield-warning-bold-duotone"}
This component uses the [lightweight-charts](https://github.com/tradingview/lightweight-charts) library, which is a top performing financial charting library.
:::

Display charts and graphs with the Lightweight Charts™ library.

## Example

<!-- demo: #examples/lightweight-charts/base -->

## Components

### AddonLightweightCharts

:doc-component-meta{name="AddonLightweightCharts"}
