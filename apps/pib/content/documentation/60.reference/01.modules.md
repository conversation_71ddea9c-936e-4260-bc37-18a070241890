---
title: <PERSON>ro Modules
---

# Modules

## Tairo Modules

This is a list of modules that are preinstalled with Tairo layer.

| Module | Description |
| :--- | :--- |
| [@shuriken-ui/nuxt](https://shurikenui.com/) | Base UI components |
| [nuxt-icon](https://github.com/nuxt-modules/icon) | 100,000+ ready to use icons from Iconify. |
| [@nuxtjs/color-mode](https://color-mode.nuxtjs.org/) | Dark and Light mode for Nuxt with auto detection |

## Additional modules in Tairo demo

These modules are not preinstalled with Tairo layer, but they are used in Tairo demo.

| Module | Description |
| :--- | :--- |
| [reka-ui](https://reka-ui.com/docs/overview/installation#nuxt-modules) | Auto import Reka UI components |
| [@vueuse/nuxt](https://vueuse.org/) | Vue composition API utilities |
| [@nuxt/image](https://image.nuxt.com/) | Plug-and-play image optimization for Nuxt apps. |
| [@nuxt/fonts](https://fonts.nuxt.com/) | Plug-and-play fonts optimization for Nuxt apps. |
| [@nuxt/eslint](https://eslint.nuxt.com/) | Collection of ESLint-related packages for Nuxt |
| [@nuxt/content](https://content.nuxt.com/) | Allows developers to write their content in Markdown |
| [@nuxtjs/i18n](https://i18n.nuxtjs.org/) | I18n (Internationalization) module for Nuxt |

## Other Recommended Modules

These modules are not preinstalled with Tairo layer nor used in Tairo demo, but they are recommended by Tairo team.

| Module | Description |
| :--- | :--- |
| [nuxt-auth-utils](https://github.com/atinux/nuxt-auth-utils) | Add Authentication to Nuxt applications with secured & sealed cookies sessions. |
| [@nuxt/scripts](https://scripts.nuxt.com/) | Nuxt Scripts lets you load third-party scripts with better performance, privacy, security and DX |
| [@pinia/nuxt](https://pinia.vuejs.org/ssr/nuxt.html) | The intuitive store for Vue and Nuxt |
| [@pinia/colada-nuxt](https://pinia-colada.esm.dev/nuxt.html) | Makes handling async state management in pinia a breeze |
