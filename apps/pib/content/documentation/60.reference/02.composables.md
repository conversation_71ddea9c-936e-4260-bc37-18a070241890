---
title: <PERSON><PERSON> Composables
---

# Composables

| Module | Description |
| :--- | :--- |
| [useMultiStepForm](/documentation/guides/mutli-step-forms) | A composable to create forms over multiple pages |
| [usePanels](/documentation/guides/panels) | Easily create and show panels with a lot of features |
| _useTailwindBreakpoints_ | Get different breakpoints from the current screen size |
| _useIsMacLike_ | Check if the current platform is a Mac or iOS device |
| _useMetaKey_ | Get the meta key for the current platform (`⌘` on Mac, `ctrl` on Windows) |
| [useLayoutCollapseContext](/documentation/layout/collapse) | Get the collapse layout context |
| [useLayoutSidebarContext](/documentation/layout/sidebar) | Get the sidebar layout context |
| [useLayoutSidenavContext](/documentation/layout/sidenav) | Get the sidenav layout context |
| [useLayoutTopnavContext](/documentation/layout/topnav) | Get the topnav layout context |
