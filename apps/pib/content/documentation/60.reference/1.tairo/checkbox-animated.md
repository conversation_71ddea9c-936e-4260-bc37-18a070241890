---
title: Checkbox animated
description: Enhanced checkbox component with smooth circular animation and multiple color variants
components:
  - TairoCheckboxAnimated
icon:
  src: /img/illustrations/components/select-icon.svg
  srcDark: /img/illustrations/components/select-icon.svg
---

# Checkbox Animated

`TairoCheckboxAnimated` is an enhanced checkbox component featuring a smooth circular animation with checkmark and customizable color variants for different states.

## Example

<!-- demo: #examples/tairo/checkbox-animated -->

## Components

## TairoCheckboxAnimated

:doc-tag{icon="nui-icon:rekaui-icon" label="CheckboxRoot" to="https://reka-ui.com/docs/components/checkbox"}
:doc-component-meta{name="TairoCheckboxAnimated"}
