---
title: Content wrapper
components:
  - TairoContentWrapper
  - TairoContentWrapperTabbed
description: Tairo content wrapper wraps the page content. It provides slots and allows you to control the horizontal flow of the page.
icon:
  src: /img/illustrations/components/logo-icon.svg
  srcDark: /img/illustrations/components/logo-icon.svg
---

# Content wrapper

The content wrapper wraps the page content. It provides slots and allows you to control the horizontal flow of the page.

## Example

### Default wrapper

:::doc-image
---
src: /img/screens/content-wrapper.png
srcDark: /img/screens/content-wrapper-dark.png
---
:::

### Tabbed wrapper

The tabbed wrapper wraps the page content. It provides slots and allows you to nest a second view inside your page.

:::doc-image
---
src: /img/screens/content-wrapper-tabbed.png
srcDark: /img/screens/content-wrapper-tabbed-dark.png
---
:::

## Components

### TairoContentWrapper

:doc-component-meta{name="TairoContentWrapper"}

### TairoContentWrapperTabbed

:doc-component-meta{name="TairoContentWrapperTabbed"}
