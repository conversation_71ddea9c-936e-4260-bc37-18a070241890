---
title: Radio Card
description: A customizable radio input component with rich styling options, built on top of Reka UI's RadioGroupItem
components:
  - TairoRadioCard
icon:
  src: /img/illustrations/components/select-icon.svg
  srcDark: /img/illustrations/components/select-icon.svg
---

# Radio Card

The `TairoRadioCard` component is a versatile radio input that combines the functionality of a radio button with the visual appeal of a card. It's built on top of Reka UI's `RadioGroupItem` and provides additional styling options through Shuriken UI's design system.

## Example

<!-- demo: #examples/tairo/radio-card -->

## Components

### TairoRadioCard

:doc-tag{icon="nui-icon:rekaui-icon" label="RadioGroupItem" to="https://reka-ui.com/docs/components/radio-group"}
:doc-component-meta{name="TairoRadioCard"}
