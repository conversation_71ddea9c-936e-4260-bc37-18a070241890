---
title: Table
components:
  - TairoTable
  - TairoTableHeading
  - TairoTableRow
  - TairoTableCell
description: Tairo tables are a quick shortcut to render lists and collections using the native HTML5 table element.
icon:
  src: /img/illustrations/components/focus-loop-icon.svg
  srcDark: /img/illustrations/components/focus-loop-icon.svg
---

# Table

Tables are a quick way to display lists and collections using the native HTML5 table element.

## Rounded examples

### Rounded:none

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/straight -->

### Rounded:sm

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/rounded -->

### Rounded:md

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/smooth -->

### Rounded:lg

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/curved -->

## Media examples

### Media:none

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/media-straight -->

### Media:sm

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/media-rounded -->

### Media:md

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/media-smooth -->

### Media:lg

Tables can have different radius factors. Use the `rounded` prop to change the table's radius.

<!-- demo: #examples/table/media-curved -->

## Components

### TairoTable

:doc-component-meta{name="TairoTable"}

### TairoTableHeading

:doc-component-meta{name="TairoTableHeading"}

### TairoTableRow

:doc-component-meta{name="TairoTableRow"}

### TairoTableCell

:doc-component-meta{name="TairoTableCell"}
