---
title: Input
description: Enhanced input component with icon support, improved styling, and seamless integration with form validation
components:
  - TairoInput
icon:
  src: /img/illustrations/components/input-icon.svg
  srcDark: /img/illustrations/components/input-icon.svg
---

# Input

`TairoInput` is an enhanced version of the Shuriken UI input component that provides built-in icon support.

## Example

<!-- demo: #examples/tairo/input -->

## Components

### TairoInput

:doc-tag{type="nui-icon:shurikenui-icon" label="BaseInput" to="https://shurikenui.com/docs/components/input"}
:doc-component-meta{name="TairoInput"}
