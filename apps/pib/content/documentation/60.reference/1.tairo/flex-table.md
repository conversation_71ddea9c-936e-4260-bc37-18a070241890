---
title: Flex table
components:
  - TairoFlexTable
  - TairoFlexTableHeading
  - TairoFlexTableRow
  - TairoFlexTableCell
description: Flex tables are an alternative way to display tabular data. They behave responsively on smaller screens.
icon:
  src: /img/illustrations/components/table-icon.svg
  srcDark: /img/illustrations/components/table-icon.svg
---

# Flex Table

Flex tables are an alternative way to display tabular data. They behave responsively on smaller screens.

## Rounded examples

### Rounded:none

Flex tables can have different radius factors. Use the `rounded` prop to change the radius of the table. This example uses the `none` radius.

<!-- demo: #examples/flex-table/straight -->

### Rounded:sm

Flex tables can have different radius factors. Use the `rounded` prop to change the radius of the table. This example uses the `sm` radius.

<!-- demo: #examples/flex-table/rounded -->

### Rounded:md

Flex tables can have different radius factors. Use the `rounded` prop to change the radius of the table. This example uses the `md` radius.

<!-- demo: #examples/flex-table/smooth -->

### Rounded:lg

Flex tables can have different radius factors. Use the `rounded` prop to change the radius of the table. This example uses the `lg` radius.

<!-- demo: #examples/flex-table/curved -->

## Components

### TairoFlexTable

:doc-component-meta{name="TairoFlexTable"}

### TairoFlexTableHeading

:doc-component-meta{name="TairoFlexTableHeading"}

### TairoFlexTableRow

:doc-component-meta{name="TairoFlexTableRow"}

### TairoFlexTableCell

:doc-component-meta{name="TairoFlexTableCell"}
