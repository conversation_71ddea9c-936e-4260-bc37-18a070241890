---
title: Form save
description: Tairo form save is a customizable set of fixed buttons that are used to save or cancel a form.
components:
  - TairoFormSave
icon:
  src: /img/illustrations/components/button-action-icon.svg
  srcDark: /img/illustrations/components/button-action-icon.svg
---

# Form save

Display floating submit and reset button when users scroll. Wrap this component in a `<form>` element or use the `#default` slot to insert your own component

## Example

<!-- demo: #examples/tairo/form-save -->

## Components

### TairoFormSave

:doc-component-meta{name="TairoFormSave"}
