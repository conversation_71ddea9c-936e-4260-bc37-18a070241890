---
title: Form group
description: Tairo form groups are used to hold some form elements together. They are used to show a label and a description for a group of form elements.
components:
  - TairoFormGroup
icon:
  src: /img/illustrations/components/button-close-icon.svg
  srcDark: /img/illustrations/components/button-close-icon.svg
---

# Form Group

Use the form group component to hold some form elements together. It is used to show a label and a description for a group of form elements. The form group component is root is an HTML `<fieldset />` element.

## Example

<!-- demo: #examples/tairo/form-group -->

## Components

### TairoFormGroup

:doc-component-meta{name="TairoFormGroup"}
