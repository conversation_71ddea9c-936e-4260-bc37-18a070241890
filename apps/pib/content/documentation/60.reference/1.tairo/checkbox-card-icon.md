---
title: Checkbox card icon
description: Enhanced checkbox component with icon support and card-based styling for visual selection
components:
  - TairoCheckboxCardIcon
icon:
  src: /img/illustrations/components/select-icon.svg
  srcDark: /img/illustrations/components/select-icon.svg
---

# Checkbox Card Icon

`TairoCheckboxCardIcon` is an enhanced checkbox component that combines icon support with card-based styling for visual selection states.

## Example

<!-- demo: #examples/tairo/checkbox-card-icon -->

## Components

### TairoCheckboxCardIcon

:doc-tag{ion="nui-icon:rekaui-icon" label="CheckboxRoot" to="https://reka-ui.com/docs/components/checkbox"}
:doc-component-meta{name="TairoCheckboxCardIcon"}
