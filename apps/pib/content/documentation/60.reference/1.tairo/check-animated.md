---
title: Check animated
description: Tairo animated check icon can be used to show a checkmark animation when a user accomplishes an action.
components:
  - TairoCheckAnimated
icon:
  src: /img/illustrations/components/checkbox-icon.svg
  srcDark: /img/illustrations/components/checkbox-icon.svg
---

# Check animated

`TairoCheckAnimated` is a simple animated icon that can be used to show a checkmark animation when a user accomplishes an action. It can be used in a button or in a form to show that the action was successful.

## Example

<!-- demo: #examples/tairo/check-animated -->

## Components

### TairoCheckAnimated

:doc-component-meta{name="TairoCheckAnimated"}
