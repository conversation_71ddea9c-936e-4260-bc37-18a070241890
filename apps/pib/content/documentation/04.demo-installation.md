---
title: Demo installation
---

# Demo installation

This guide will help you run the Tairo demo app from the source code.

## Overview

The demo app is a separate project. It include source code of [tairo.cssninja.io](https://tairo.cssninja.io) and is used to showcase the components and features of the design system.

It's a good starting point to see how <PERSON><PERSON> is used to build a dashboard application.

### Features

::doc-checklist
---
items:
  - Nuxt 4 compatibility mode
  - Nuxt content integration
  - Nuxt i18n integration
  - Prebuilt layouts
  - Prebuilt pages
  - Prebuilt api routes
  - Addon components (with external dependencies)
---
::

## Install and run from the source

### Download the source code

You need to get the source of <PERSON><PERSON> to run the demo. For this, you can either download it from your purchases on themeforest.net, cssninja.io or clone the repository from Github once you have unlocked your access.

The project structure should look like this:

:::code-group{expandable}
```bash [Terminal]
├── .app/ # This is the entry point of the quickstarter
├── .demo/ # This is the entry point of the demo
├── .vscode/ # Predefined vscode settings
├── layers/
│   └── tairo/ # The Tairo layer
├── patches/ # Contains patches for dependencies (pnpm patches)
├── # Predefined dotfiles
├── .editorconfig
├── .gitignore
├── # Monorepo configuration files
├── package.json
├── eslint.config.mjs
├── tsconfig.json
├── pnpm-lock.yaml
└── pnpm-workspace.yaml
```
:::

The demo app is located in the `.demo` directory with the following structure:

:::code-group{expandable}
```bash [Terminal]
.demo/
├── app/
│   ├── assets/
│   ├── components/
│   ├── composables/
│   ├── layouts/
│   ├── pages/
│   ├── types/
│   ├── utils/
│   ├── app.config.ts
│   ├── app.vue
│   └── error.vue
├── content/
├── examples/
├── i18n/
├── modules/
├── public/
├── server/
│   ├── api/
│   └── tsconfig.json
├── patches/ # Contains patches for dependencies (pnpm patches)
├── # Predefined dotfiles
├── .editorconfig
├── .gitignore
├── content.config.ts
├── nuxt.config.ts
├── package.json
├── eslint.config.mjs
└── tsconfig.json
```
:::

### Install dependencies with pnpm

When using Tairo from source, **pnpm is required** in order to work. This is because we rely on the `pnpm-workspace.yaml` to manage the dependencies between layers.

At the root of the project, run the following command to install the dependencies:

::code-group
```bash [Terminal]
pnpm install
```
::

### Run the demo development server

Now you can start the development server to see the demo app in action.

::code-group
```bash [Terminal]
pnpm --filter=demo dev
```
::

You can now access the demo app in your browser at [http://localhost:3000/](http://localhost:3000/).

## Discover components

You will see that most pages are built with reusable components, some are prefixed by `Base`, `Tairo`, `Demo` and a few with `Addon`.

| Prefix | Description |
|--------|-------------|
| `Base` | `@shuriken-ui/nuxt` components, sometimes known as atoms, like buttons, inputs, etc. Ready to use base components. |
| `Tairo` | Components specific to the project layouts, or reusable components with no dependencies. Ready to use Tairo components or components specific to a layout (like the sidebar, collapse, etc.). |
| `Addon` | Components with external dependencies, like the markdown editor, charts, etc. You may need to install additional packages and copy them into your project. |
| `Demo` | Components specific to the demo, like the quick search, widgets, etc. Example components specific to the demo. Use them as inspiration for your own components. |
| `Doc` | Components specific to the documentation, like the code block, markdown, etc. Related to `@nuxt/content` and `nuxt-component-meta` modules. |
